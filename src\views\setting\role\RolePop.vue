<template>
  <div>
    <Layer ref="layerRef" :title="isEdit?'修改角色':'添加角色'"  @fatherShow='close(formRef)'>
      <template #layer>
        <div>
          <el-form ref="formRef" :model="form" :rules="rules" label-width="auto">
            <el-form-item label="排序" prop="sort">
              <el-input  v-model="form.sort" placeholder="排序" type="number"  />
            </el-form-item>
            <el-form-item label="名称" prop="name">
              <el-input  v-model="form.name" placeholder="名称"  />
            </el-form-item>
            <el-form-item label="备注" prop="memo">
              <el-input  v-model="form.memo" placeholder="备注"  />
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option label="启用" :value="1" />
                <el-option label="禁用" :value="0" />
              </el-select>
            </el-form-item>
            <el-form-item label="权限" prop="ruleIds">
              <el-tree
                class="treeBox"
                ref="treeRef"
                :data="menuList"
                show-checkbox
                default-expand-all
                node-key="id"
                highlight-current
                :props="defaultProps"
                :default-checked-keys="form.ruleIds"
              />
            </el-form-item>
          </el-form>

          <div class="popBtn">
              <el-button type="" size="small" @click="close(formRef)">取消</el-button>
              <el-button v-if="isEdit" type="primary"  size="small" @click="save(formRef)">保存</el-button>
              <el-button v-else type="primary"  size="small" @click="submit(formRef)">确定</el-button>
            </div>
        </div>
      </template>
    </Layer>
  </div>
</template>
<script lang="ts" setup>
import { ref,reactive } from 'vue'
import Layer from '@/components/Layer.vue'
import { roleAddApi,roleUpdateApi,roleInfoApi } from '@/api/api'
import {type FormInstance ,type FormRules,ElMessage } from 'element-plus'

let isEdit = ref(false)
let menuList = ref([])
const defaultProps = reactive({
  label:'name',
  children:'children'
})

const layerRef = ref()
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' },
  ],
})

interface formType {
  id ?:any
  sort:number,
  name:string,
  memo:string,
  status:number
  ruleIds:number[]
}

let form:Ref<formType> = ref ({
  sort:0,
  name:'',
  memo:'',
  status:1,
  ruleIds: []
})
const emit = defineEmits(['changeData'])


const open = ()=>{
  layerRef.value.showModel = true
}
const close = (formEl: FormInstance | undefined)=>{
  if (!formEl) return
  formEl.resetFields()
  menuList.value = []
  layerRef.value.showModel = false
}

const getDetail = (id:number)=>{
  roleInfoApi(id,{}).then((res:any)=>{
    console.log(res);
    if(res.code == 0){
      form.value = res.data.Role
      menuList.value.forEach((item:any) =>{
        if(res.data.ruleIds.indexOf(item.id) != -1){
          res.data.ruleIds.splice(res.data.ruleIds.indexOf(item.id),1)
        }
      })
      form.value.ruleIds = res.data.ruleIds
    }else{
      ElMessage.error(res.msg)
    }
  })
}

const treeRef = ref()
const formRef = ref<FormInstance>()
const submit = (formEl:FormInstance | undefined )=>{
  if (!formEl) return
  if( treeRef.value.getCheckedNodes().length > 0){
    treeRef.value.getCheckedNodes().forEach((item:any) =>{
      form.value.ruleIds.push(item.id)
    })
  }
  if( treeRef.value.getHalfCheckedNodes().length > 0){
    treeRef.value.getHalfCheckedNodes().forEach((item:any) =>{
      form.value.ruleIds.push(item.id)
    })
  }
  if(form.value.ruleIds.length == 0){
    ElMessage.info('请选择权限')
    return
  }
  formEl.validate((valid) => {
    if (valid) {
      console.log(form.value);
      roleAddApi(form.value).then((res:any)=>{
        console.log(res);
        if(res.code == 0){
          ElMessage.success('成功')
          emit('changeData')
          close(formRef.value)
        }else{
          ElMessage.error(res.msg)
        }
      })
    }
  })
}

const save = (formEl:FormInstance | undefined)=>{
  console.log(form.value);
  if (!formEl) return
  form.value.ruleIds = []
  if( treeRef.value.getCheckedNodes().length > 0){
    treeRef.value.getCheckedNodes().forEach((item:any) =>{
      form.value.ruleIds.push(item.id)
    })
  }
  if( treeRef.value.getHalfCheckedNodes().length > 0){
    treeRef.value.getHalfCheckedNodes().forEach((item:any) =>{
      form.value.ruleIds.push(item.id)
    })
  }
  if(form.value.ruleIds.length == 0){
    ElMessage.info('请选择权限')
    return
  }
  formEl.validate((valid) => {
    if (valid) {
      console.log(form);
      roleUpdateApi(form.value.id,form.value).then((res:any)=>{
        console.log(res);
        if(res.code == 0){
          ElMessage.success(res.msg?res.msg:'成功')
          emit('changeData')
          close(formRef.value)
        }else{
          ElMessage.error(res.msg)
        }
      })
    }
  })
}

defineExpose({
  isEdit,
  form,
  open,
  menuList,
  getDetail
})
</script>

<style lang="scss" scoped>
.treeBox{
  display: flex;
}

</style>