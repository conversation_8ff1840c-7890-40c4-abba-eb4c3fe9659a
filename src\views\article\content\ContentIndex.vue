<template>
  <div>
    <h4 class="mb_2">{{$route.params.catName}}</h4>
    <div>
      <el-form class="form" ref="formRef" :model="form" :rules="rules" label-width="auto">
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
          <el-tab-pane label="常规" name="first">
            <el-form-item label="排序" prop="sort">
              <el-input  v-model="form.sort" placeholder="排序" type="number" />
            </el-form-item>
            <el-form-item label="标题" prop="title">
              <el-input  v-model="form.title" placeholder="标题"  />
            </el-form-item>
            
            <el-form-item label="封面图" prop="pic">
              <el-upload
                accept=".png,.jpg,.jpeg"
                :file-list="fileList"
                action=""
                :multiple="false"
                :before-upload="beforeUpload"
                :before-remove="beforeRemove"
                :on-change="fileChange"
                :on-exceed="handleExceed"
                :limit="1"
                :with-credentials="true"
              >
                <el-button v-if="!form.pic" type="primary" size="small">上传</el-button>
                <div v-else>
                  <el-image  style="height: 100px;" :src="store.hw + form.pic" />
                  <el-icon  @click.stop="form.pic = ''"><CircleCloseFilled /></el-icon>
                </div>
              </el-upload>
            </el-form-item>
            <el-form-item label="内容" prop="content">
              <WangEditor style="width: 100%;z-index:999;" v-model="form.content" :initValue="form.content"  @getEditorContent="onEditorChange" />
            </el-form-item>
            <el-form-item label="置顶" prop="top">
              <el-radio-group v-model="form.top">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="展示" prop="display">
              <el-radio-group v-model="form.display">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="驳回原因" prop="reason" v-if="form.reason">
              <el-input  v-model="form.reason" disabled  />
            </el-form-item>
            <el-form-item label="审核" prop="audit" >
              <el-radio-group v-model="form.audit" disabled>
                <el-radio :label="0">待审核</el-radio>
                <el-radio :label="1">通过</el-radio>
                <el-radio :label="9">拒绝</el-radio>
              </el-radio-group>
            </el-form-item>
            
          </el-tab-pane><el-tab-pane label="其他" name="third">
            <el-form-item label="作者" prop="author">
              <el-input  v-model="form.author" />
            </el-form-item>
            <el-form-item label="来源" prop="source" >
                  <el-input  v-model="form.source" :disabled="form.audit == 1" />
                </el-form-item>
            <el-form-item label="浏览量" prop="views">
              <el-input  v-model="form.views"  />
            </el-form-item>
            <el-form-item label="发布时间" prop="pubtime" >
              <el-date-picker
                v-model="form.pubtime"
                type="datetime"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                :disabled="form.audit == 1"
              />
            </el-form-item>
            <el-form-item label="结束时间" prop="endtime" >
              <el-date-picker
                v-model="form.endtime"
                type="datetime"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                :disabled="form.audit == 1"
              />
            </el-form-item>
            <el-form-item label="关键字" prop="keywords">
              <el-input  v-model="form.keywords" placeholder="关键字"  />
            </el-form-item>
            <el-form-item label="简介" prop="description">
              <el-input  v-model="form.description" placeholder="简介" type="textarea"   />
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="模板" name="four">
            <el-form  label-width="auto">
              <el-form-item v-for="(item,index) in modelField" :key="item.id"  :label="item.fieldLabel" >
                <el-input v-if="item.fieldType == 'text'"  v-model="item.defaultValue"  />
                <el-input v-if="item.fieldType == 'multiText'"  v-model="item.defaultValue"  />
                <el-radio-group v-if="item.fieldType == 'radio'" v-model="item.defaultValue">
                  <el-radio v-for="(child) in item.fieldValues" :key="child.value" :label="child.value">{{child.name}}</el-radio>
                </el-radio-group>
                <el-checkbox-group v-if="item.fieldType == 'check'" v-model="item.defaultValue">
                  <el-checkbox v-for="(child) in item.fieldValues" :key="child.value" :label="child.value" >{{child.name}}</el-checkbox>
                </el-checkbox-group>
                <el-select v-if="item.fieldType == 'select'" v-model="item.defaultValue" >
                  <el-option
                    v-for="(child) in item.fieldValues"
                    :key="child.value"
                    :label="child.name"
                    :value="child.value"
                  />
                </el-select>
                <el-select v-if="item.fieldType == 'multiSelect'" v-model="item.defaultValue" multiple>
                  <el-option
                    v-for="(child) in item.fieldValues"
                    :key="child.value"
                    :label="child.name"
                    :value="child.value"
                  />
                </el-select>
                <el-upload
                  v-if="item.fieldType == 'picture'"
                  accept=".png,.jpg,.jpeg"
                  action=""
                  :multiple="false"
                  :before-upload="beforeUpload1"
                  :before-remove="beforeRemove1"
                  :on-change="fileChange1"
                  :on-exceed="handleExceed"
                  :limit="1"
                  :with-credentials="true"
                >
                  <el-button v-if="!item.defaultValue" type="primary" size="small" @click="uploadImg(index)">上传</el-button>
                  <el-image  v-else style="height: 100px" :src="store.hw + item.defaultValue" />
                </el-upload>
                <div v-if="item.fieldType == 'multiPicture'">
                  <el-upload
                    accept=".png,.jpg,.jpeg"
                    action=""
                    :before-upload="beforeUpload"
                    :show-file-list = 'false'
                  >
                    <el-button  type="primary" size="small" @click="uploadImg(index)">上传</el-button>
                  </el-upload>
                  <div v-if="item.defaultValue.length > 0">
                    <el-image  v-for="(img,imgIndex) in item.defaultValue" :key="img" style="height: 100px" :src="store.hw + img" @click="delImg(imgIndex)" />
                  </div>
                </div>
                <WangEditor v-if="item.fieldType == 'richText'"  :initValue="item.defaultValue"  />
                <el-input v-if="item.fieldType == 'number'"  v-model="item.defaultValue" type="number"  />
                <el-date-picker
                  v-if="item.fieldType == 'date'"
                  v-model="item.defaultValue"
                  type="date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
                <el-date-picker
                  v-if="item.fieldType == 'datetime'"
                  v-model="item.defaultValue"
                  type="datetime"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
                <el-upload
                  v-if="item.fieldType == 'file'"
                  accept=""
                  action=""
                  :multiple="false"
                  :before-upload="beforeUpload1"
                  :on-exceed="handleExceed"
                  :limit="1"
                  :with-credentials="true"
                >
                  <el-button v-if="!item.defaultValue" type="primary" size="small" @click="uploadImg(index)">上传文件</el-button>
                  <a v-else :href="store.hw + item.defaultValue">文件</a>
                </el-upload>

              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <div class="popBtn">
        <el-button type=""  @click="close()">重置</el-button>
        <el-button v-if="saveArticle" type="primary"   @click="save(formRef)">保存</el-button>
        <el-button v-else type="primary"   @click="add(formRef)">添加</el-button>
        <el-button type="success" plain icon="view" @click="view()">预览</el-button>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref,reactive,onMounted,watch } from 'vue'
import { artArticleListApi,uploadApi,artArticleInfoApi,artArticleUpdateApi,artArticleAddApi } from '@/api/api'
import {type FormInstance ,type FormRules,ElMessage, type UploadProps, UploadUserFile,type TabsPaneContext } from 'element-plus'
import { useStore } from '@/store/Index'
const store = useStore()
import { useRoute,useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
import { getDate } from '@/api/tool'

const activeName = ref('first')
const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event)
}

const rules = reactive<FormRules>({
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' },
  ],
  display: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
})
let form = ref ({
  id:'',
  sort:0,
  title:'',
  pic:'',
  content:'',
  display:1,
  keywords:'',
  description:'',
  author:'',
  source:'',
  views:0,
  link:'',
  pubtime:getDate('yyyy-MM-dd hh:mm:ss',new Date().getTime()),
  endtime:'',
  modelData:'',
  top:0,
  audit:0,
  reason:''
})


// 模型
let modelField = ref()

watch(()=>router.currentRoute.value,(newValue:any)=>{
  console.log(newValue)
  if(newValue.name == 'contentIndex'){
    getIndex()
  }
})

onMounted(()=>{
  getIndex()
})

let saveArticle = ref(false)

const getIndex = ()=>{
  artArticleListApi({
    catId:route.params.catId
  }).then((res:any) =>{
    console.log('文章列表',res)
    if(res.code == 0){
      if(res.data.length > 0){
        saveArticle.value = true
        artArticleInfoApi(res.data[0].id).then((res:any)=>{
        console.log('详情',res)
          if(res.code == 0){
            form.value = res.data
            form.value.content = form.value.content?form.value.content:''
          }else{
            ElMessage.error(res.msg)
          }
        })
      }
      
    }
  })
}

const onEditorChange = (html:string) => {
  // console.log(html)
  form.value.content = html
}

const close = ()=>{
  form.value = {
    id:'',
    sort:0,
    title:'',
    pic:'',
    content:'',
    display:1,
    keywords:'',
    description:'',
    author:'',
    source:'',
    views:0,
    link:'',
    pubtime:getDate('yyyy-MM-dd hh:mm:ss',new Date().getTime()),
    endtime:'',
    modelData:'',
    top:0,
    audit:0,
    reason:''
  }
}

const formRef = ref<FormInstance>()
const save = (formEl:FormInstance | undefined)=>{
  console.log(form.value);
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      console.log(form);
      form.value.modelData = ''
      artArticleUpdateApi(form.value).then((res:any)=>{
        console.log(res);
        if(res.code == 0){
          ElMessage.success(res.msg)
          getIndex()
        }else{
          ElMessage.error(res.msg)
        }
      })
    }
  })
}

const add = (formEl:FormInstance | undefined)=>{
  console.log(form.value);
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      console.log(form);
      form.value.modelData = ''
      artArticleAddApi({
        ...form.value,
        catId:route.params.catId
      }).then((res:any)=>{
        console.log(res);
        if(res.code == 0){
          ElMessage.success(res.msg)
          getIndex()
        }else{
          ElMessage.error(res.msg)
        }
      })
    }
  })
}

// 上传
let fileList = ref<UploadUserFile[]>([])
const handleExceed: UploadProps['onExceed'] = ()=>{
  ElMessage.warning('当前限制选择1个文件')
}
const fileChange: UploadProps['onChange'] = (file)=>{
  fileList.value = [file]
}
const beforeRemove: UploadProps['beforeRemove'] = (file, fileLists)=>{
  console.log(file, fileLists);
  fileList.value  = []
  return true
}
const beforeUpload: UploadProps['beforeUpload'] = (file)=>{
  console.log('文件',file);
  const isLt100M = file.size / 1024 / 1024 < 10;
  if (!isLt100M) {
    ElMessage.warning("大小不得超过10M");
    return false;
  }
  let params = new FormData();
  params.append("file", file);
  params.append("isTemp","forever");
  params.append("uType","admin");
  uploadApi(params).then((res:any)=>{
    console.log(res);
    if(res.code == 0){
      form.value.pic =  res.data
    }else{
      ElMessage.error(res.msg)
    }
  })
  return false;
}

let fileList1 = ref<UploadUserFile[]>([])
let uploadIndex = ref(0)
const uploadImg = (index:number)=>{
  uploadIndex.value = index
}
const delImg = (index:number)=>{
  modelField.value[uploadIndex.value].defaultValue.splice(index,1)
}

const fileChange1: UploadProps['onChange'] = (file)=>{
  fileList1.value = [file]
}
const beforeRemove1: UploadProps['beforeRemove'] = (file, fileLists)=>{
  console.log(file, fileLists);
  fileList1.value  = []
  return true
}
const beforeUpload1: UploadProps['beforeUpload'] = (file)=>{
  console.log('文件',file);
  const isLt100M = file.size / 1024 / 1024 < 10;
  if (!isLt100M) {
    ElMessage.warning("大小不得超过10M");
    return false;
  }
  let params = new FormData();
  params.append("file", file);
  params.append("isTemp","forever");
  params.append("uType","admin");
  uploadApi(params).then((res:any)=>{
    console.log(res);
    if(res.code == 0){
      modelField.value[uploadIndex.value].defaultValue.push(res.data)
    }else{
      ElMessage.error(res.msg)
    }
  })
  return false;
}

const view = () => {
  const url = router.resolve({
    name:'articleIndex',
    params:{
      id:form.value.id
    },
  })
  window.open(url.href,"_blank")
}



</script>
<style lang="scss" scoped>
h4{
  border-bottom: 1px solid #e5e6eb ;
}
:deep(.el-tabs__content){
  height: calc(100vh - 270px);
}
:deep(.el-tabs__content){
  max-height: 70vh;
  overflow: auto;
}
</style>