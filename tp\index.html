{extend name="public/base"} {block name="title"}{/block} {block name="head"}
<meta name="description" content="{$Aconfigs.brief}" />
<meta name="keywords" content="{$Aconfigs.keywords}" />
<link rel="stylesheet" href="__CSS__/index.css">
<link rel="stylesheet" href="__CSS__/swiper-bundle.min.css">
{/block} {block name="main"}
<!-- pos:index_1 -->
<?php $posKey = 'index_1'; $Pos = $Poss[$posKey]; ?>
{if condition="!empty($Entrances[$posKey])"}
<?php $PosEntrances = $Entrances[$posKey]; ?>
<div class="banner_one">
    {notempty name="$Pos.titlePic"}
        <img class="logo" src="{$Pos.titlePic}" alt="">
    {/notempty}
    <div class="swiper container swiper_index_1">
        <div class="swiper-wrapper">
            {volist name="PosEntrances" id="Entrance"}
                {eq name="$Entrance->pos" value="$Pos.posKey"}
                    <div class="swiper-slide">
                        <a href="{$Entrance->link}" target="_blank" title="{$Entrance->name}">
                            <img src="{$Entrance->path|filePreview}" alt="">
                        </a>
                    </div>
                {/eq}
            {/volist}
        </div>
        <!-- 如果需要分页器 -->
        <div class="swiper-pagination"></div>
    </div>
</div>
{/if}

<!-- cat1: banner + arts -->
<?php $ArticleAndCat = array_shift($Articles); ?>
{notempty name="ArticleAndCat"}
<?php $Cat = $ArticleAndCat['Cat']; ?>
<?php $Arts = $ArticleAndCat['Arts']; ?>
<div class="box1">
    <div class="container">
        <div class="second_tit flex_between">
            <div>
                <h2 class="fz24">{$Cat->title}</h2>
                {notempty name="Cat->subtitle"}
                    <span class="fz14"> /{$Cat->subtitle}</span>
                {/notempty}
            </div>
            <a class="more flex_center fz14" href="/catgory/{$Cat->id}.html">
                更多
                <img src="__IMG__/more.png" alt="">
            </a>
        </div>
        <div class="con">
            <div class="content_left swiper1 swiper_article_1">
                <div class="swiper-wrapper">
                    {volist name="BannerArts" id="Art"}
                        <div class="swiper-slide">
                            <a href="/article/{$Art->catId}/{$Art->id}.html">
                                <img src="{$Art->pic|filePreview}" alt="" height="400px">
                                <h3 class="elli fz16">{$Art->title}</h3>
                            </a>
                        </div>
                    {/volist}
                </div>
                <!-- 如果需要分页器 -->
                <div class="swiper-pagination"></div>
                <!-- //如果需要导航按钮 -->
                <div class="swiper-button-prev"></div>
                <div class="swiper-button-next"></div>
            </div>
            <div class="content_right">
                <ul>
                    {volist name="Arts" id="Art"}
                        <li>
                            <a href="/article/{$Art->catId}/{$Art->id}.html">
                                <div class="date">
                                    <span class="fz32 flex_center">{$Art->pubtime|date="d"}</span>
                                    <span class="fz16 flex_center">{$Art->pubtime|date="Y-m"}</span>
                                </div>
                                <div class="content_right_con">
                                    <h3 class="elli fz16">{$Art->title}</h3>
                                </div>
                            </a>
                        </li>
                    {/volist}
                </ul>
            </div>
        </div>
    </div>
</div>
{/notempty}

<!-- pos:index_2 -->
<?php $posKey = 'index_2'; $Pos = $Poss[$posKey]; ?>
{if condition="!empty($Entrances[$posKey])"}
<?php $PosEntrances = $Entrances[$posKey]; ?>
<div class="banner_one">
    {notempty name="$Pos.titlePic"}
        <img class="logo" src="{$Pos.titlePic}" alt="">
    {/notempty}
    <div class="swiper container swiper_index_2">
        <div class="swiper-wrapper">
            {volist name="PosEntrances" id="Entrance"}
                {eq name="$Entrance->pos" value="$Pos.posKey"}
                    <div class="swiper-slide">
                        <a href="{$Entrance->link}" target="_blank" title="{$Entrance->name}">
                            <img src="{$Entrance->path|filePreview}" alt="">
                        </a>
                    </div>
                {/eq}
            {/volist}
        </div>
        <!-- 如果需要分页器 -->
        <div class="swiper-pagination"></div>
    </div>
</div>
{/if}

<!-- art cat X 2 -->
<div class="box3">
    <div class="container ">
        <?php $ArticleAndCat = array_shift($Articles); ?>
        {notempty name="ArticleAndCat"}
        <?php $Cat = $ArticleAndCat['Cat']; ?>
        <?php $Arts = $ArticleAndCat['Arts']; ?>
        <div class="box3_left fff">
            <div class="second_tit flex_between">
                <div>
                    <h2 class="fz24">{$Cat->title}</h2>
                    {notempty name="Cat->subtitle"}
                        <span class="fz14"> /{$Cat->subtitle}</span>
                    {/notempty}
                </div>
                <a class="more flex_center fz14" href="/catgory/{$Cat->id}.html">
                    更多
                    <img src="__IMG__/more.png" alt="">
                </a>
            </div>
            <ul>
                {volist name="Arts" id="Art"}
                <li class="flex_between">
                    <a class="elli fz16" href="/article/{$Art->catId}/{$Art->id}.html">
                        <img src="__IMG__/left1.png" alt="">
                        {$Art->title}
                    </a>
                    <span class="fz14">{$Art->pubtime|date="Y-m-d"}</span>
                </li>
                {/volist}
            </ul>
        </div>
        {/notempty}
        <?php $ArticleAndCat = array_shift($Articles); ?>
        {notempty name="ArticleAndCat"}
        <?php $Cat = $ArticleAndCat['Cat']; ?>
        <?php $Arts = $ArticleAndCat['Arts']; ?>
        <div class="box3_right fff">
            <div class="second_tit flex_between">
                <div>
                    <h2 class="fz24">{$Cat->title}</h2>
                    {notempty name="Cat->subtitle"}
                        <span class="fz14"> /{$Cat->subtitle}</span>
                    {/notempty}
                </div>
                <a class="more flex_center fz14" href="/catgory/{$Cat->id}.html">
                    更多
                    <img src="__IMG__/more.png" alt="">
                </a>
            </div>
            <ul>
                {volist name="Arts" id="Art"}
                <li class="flex_between">
                    <a class="elli fz16" href="/article/{$Art->catId}/{$Art->id}.html">
                        <img src="__IMG__/left1.png" alt="">
                        {$Art->title}
                    </a>
                    <span class="fz14">{$Art->pubtime|date="Y-m-d"}</span>
                </li>
                {/volist}
            </ul>
        </div>
        {/notempty}
    </div>
</div>
<!-- art cat X 2 -->
<div class="box3">
    <div class="container">
        <?php $ArticleAndCat = array_shift($Articles); ?>
        {notempty name="ArticleAndCat"}
        <?php $Cat = $ArticleAndCat['Cat']; ?>
        <?php $Arts = $ArticleAndCat['Arts']; ?>
        <div class="box3_left fff">
            <div class="second_tit flex_between">
                <div>
                    <h2 class="fz24">{$Cat->title}</h2>
                    {notempty name="Cat->subtitle"}
                        <span class="fz14"> /{$Cat->subtitle}</span>
                    {/notempty}
                </div>
                <a class="more flex_center fz14" href="/catgory/{$Cat->id}.html">
                    更多
                    <img src="__IMG__/more.png" alt="">
                </a>
            </div>
            <ul>
                {volist name="Arts" id="Art"}
                <li class="flex_between">
                    <a class="elli fz16" href="/article/{$Art->catId}/{$Art->id}.html">
                        <img src="__IMG__/left1.png" alt="">
                        {$Art->title}
                    </a>
                    <span class="fz14">{$Art->pubtime|date="Y-m-d"}</span>
                </li>
                {/volist}
            </ul>
        </div>
        {/notempty}
        <?php $ArticleAndCat = array_shift($Articles); ?>
        {notempty name="ArticleAndCat"}
        <?php $Cat = $ArticleAndCat['Cat']; ?>
        <?php $Arts = $ArticleAndCat['Arts']; ?>
        <div class="box3_right fff">
            <div class="second_tit flex_between">
                <div>
                    <h2 class="fz24">{$Cat->title}</h2>
                    {notempty name="Cat->subtitle"}
                        <span class="fz14"> /{$Cat->subtitle}</span>
                    {/notempty}
                </div>
                <a class="more flex_center fz14" href="/catgory/{$Cat->id}.html">
                    更多
                    <img src="__IMG__/more.png" alt="">
                </a>
            </div>
            <ul style="display: grid;gap:10px;grid-template-columns: repeat(5,1fr);">
                {volist name="Arts" id="Art"}
                <li class="" style="border-bottom: 0;padding: 0;">
                    <a href="/article/{$Art->catId}/{$Art->id}.html" class="fz16" style="display: flex;justify-content: center;align-items: center;flex-direction: column;width: 100%;">
                        <img src="{$Art->pic|filePreview}" alt="{$Art->title}" style="max-width: 100px;height: 80px;margin-bottom: 5px;">
                        <h4 class="elli">{$Art->title}</h4>
                    </a>
                </li>
                {/volist}
            </ul>
        </div>
        {/notempty}
    </div>
</div>

<!-- pos:index_3 -->
<?php $posKey = 'index_3'; $Pos = $Poss[$posKey]; ?>
{if condition="!empty($Entrances[$posKey])"}
<?php $PosEntrances = $Entrances[$posKey]; ?>
<div class="banner_one">
    {notempty name="$Pos.titlePic"}
        <img class="logo" src="{$Pos.titlePic}" alt="">
    {/notempty}
    <div class="swiper container swiper_index_3">
        <div class="swiper-wrapper">
            {volist name="PosEntrances" id="Entrance"}
                {eq name="$Entrance->pos" value="$Pos.posKey"}
                    <div class="swiper-slide">
                        <a href="{$Entrance->link}" target="_blank" title="{$Entrance->name}">
                            <img src="{$Entrance->path|filePreview}" alt="">
                        </a>
                    </div>
                {/eq}
            {/volist}
        </div>
        <!-- 如果需要分页器 -->
        <div class="swiper-pagination"></div>
    </div>
</div>
{/if}

<!-- art cat X 2 -->
<div class="box3">
    <div class="container ">
        <?php $ArticleAndCat = array_shift($Articles); ?>
        {notempty name="ArticleAndCat"}
        <?php $Cat = $ArticleAndCat['Cat']; ?>
        <?php $Arts = $ArticleAndCat['Arts']; ?>
        <div class="box3_left fff">
            <div class="second_tit flex_between">
                <div>
                    <h2 class="fz24">{$Cat->title}</h2>
                    {notempty name="Cat->subtitle"}
                        <span class="fz14"> /{$Cat->subtitle}</span>
                    {/notempty}
                </div>
                <a class="more flex_center fz14" href="/catgory/{$Cat->id}.html">
                    更多
                    <img src="__IMG__/more.png" alt="">
                </a>
            </div>
            <ul>
                {volist name="Arts" id="Art"}
                <li class="flex_between">
                    <a class="elli fz16" href="/article/{$Art->catId}/{$Art->id}.html">
                        <img src="__IMG__/left1.png" alt="">
                        {$Art->title}
                    </a>
                    <span class="fz14">{$Art->pubtime|date="Y-m-d"}</span>
                </li>
                {/volist}
            </ul>
        </div>
        {/notempty}
        <?php $ArticleAndCat = array_shift($Articles); ?>
        {notempty name="ArticleAndCat"}
        <?php $Cat = $ArticleAndCat['Cat']; ?>
        <?php $Arts = $ArticleAndCat['Arts']; ?>
        <div class="box3_right fff">
            <div class="second_tit flex_between">
                <div>
                    <h2 class="fz24">{$Cat->title}</h2>
                    {notempty name="Cat->subtitle"}
                        <span class="fz14"> /{$Cat->subtitle}</span>
                    {/notempty}
                </div>
                <a class="more flex_center fz14" href="/catgory/{$Cat->id}.html">
                    更多
                    <img src="__IMG__/more.png" alt="">
                </a>
            </div>
            <ul>
                {volist name="Arts" id="Art"}
                <li class="flex_between">
                    <a class="elli fz16" href="/article/{$Art->catId}/{$Art->id}.html">
                        <img src="__IMG__/left1.png" alt="">
                        {$Art->title}
                    </a>
                    <span class="fz14">{$Art->pubtime|date="Y-m-d"}</span>
                </li>
                {/volist}
            </ul>
        </div>
        {/notempty}
    </div>
</div>
<!-- art cat X 2 -->
<div class="box3">
    <div class="container">
        <?php $ArticleAndCat = array_shift($Articles); ?>
        {notempty name="ArticleAndCat"}
        <?php $Cat = $ArticleAndCat['Cat']; ?>
        <?php $Arts = $ArticleAndCat['Arts']; ?>
        <div class="box3_left fff">
            <div class="second_tit flex_between">
                <div>
                    <h2 class="fz24">{$Cat->title}</h2>
                    {notempty name="Cat->subtitle"}
                        <span class="fz14"> /{$Cat->subtitle}</span>
                    {/notempty}
                </div>
                <a class="more flex_center fz14" href="/catgory/{$Cat->id}.html">
                    更多
                    <img src="__IMG__/more.png" alt="">
                </a>
            </div>
            <ul>
                {volist name="Arts" id="Art"}
                <li class="flex_between">
                    <a class="elli fz16" href="/article/{$Art->catId}/{$Art->id}.html">
                        <img src="__IMG__/left1.png" alt="">
                        {$Art->title}
                    </a>
                    <span class="fz14">{$Art->pubtime|date="Y-m-d"}</span>
                </li>
                {/volist}
            </ul>
        </div>
        {/notempty}
        <?php $ArticleAndCat = array_shift($Articles); ?>
        {notempty name="ArticleAndCat"}
        <?php $Cat = $ArticleAndCat['Cat']; ?>
        <?php $Arts = $ArticleAndCat['Arts']; ?>
        <div class="box3_right fff">
            <div class="second_tit flex_between">
                <div>
                    <h2 class="fz24">{$Cat->title}</h2>
                    {notempty name="Cat->subtitle"}
                        <span class="fz14"> /{$Cat->subtitle}</span>
                    {/notempty}
                </div>
                <a class="more flex_center fz14" href="/catgory/{$Cat->id}.html">
                    更多
                    <img src="__IMG__/more.png" alt="">
                </a>
            </div>
            <ul>
                {volist name="Arts" id="Art"}
                <li class="flex_between">
                    <a class="elli fz16" href="/article/{$Art->catId}/{$Art->id}.html">
                        <img src="__IMG__/left1.png" alt="">
                        {$Art->title}
                    </a>
                    <span class="fz14">{$Art->pubtime|date="Y-m-d"}</span>
                </li>
                {/volist}
            </ul>
        </div>
        {/notempty}
    </div>
</div>

<!-- artx4 swiper -->
<?php $posKey = 'index_4'; $Pos = $Poss[$posKey]; ?>
{if condition="!empty($Entrances[$posKey])"}
<?php $PosEntrances = $Entrances[$posKey]; ?>
<div class="box4 border">
    <div class="container">
        <div class="second_tit flex_between">
            <div>
                <h2 class="fz24">物业配套企业</h2>
                <!-- <span class="fz14"> /Property supporting enterprises</span> -->
            </div>
        </div>
        {notempty name="$Pos.titlePic"}
            <img class="logo" src="{$Pos.titlePic}" alt="">
        {/notempty}
        <div class="swiperBox">
            <div class="swiper3 swiper_index_4">
                <div class="swiper-wrapper">
                    {volist name="PosEntrances" id="Entrance"}
                        {eq name="$Entrance->pos" value="$Pos.posKey"}
                        <div class="swiper-slide">
                            <a href="{$Entrance->link}" target="_blank" title="{$Entrance->name}">
                                <img src="{$Entrance->path|filePreview}" alt="">
                            </a>
                        </div>
                        {/eq}
                    {/volist}
                </div>
                <div class="swiper-button-prev"></div><!--左箭头。如果放置在swiper外面，需要自定义样式。-->
                <div class="swiper-button-next"></div><!--右箭头。如果放置在swiper外面，需要自定义样式。-->
            </div>
        </div>
    </div>
</div>
{/if}

{/block} {block name="js"}
<script type="text/javascript" src="__JS__/swiper-bundle.min.js"></script>
<script type="text/javascript" src="__JS__/index.js"></script>
{/block} {/extend}