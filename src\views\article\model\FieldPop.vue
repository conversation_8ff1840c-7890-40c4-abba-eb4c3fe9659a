<template>
  <div>
    <Layer ref="layerRef" :title="isEdit?'修改字段':'添加字段'"  @fatherShow='close'>
      <template #layer>
        <div>
          <el-form class="index" ref="formRef" :model="form" :rules="rules" label-width="auto">
            <el-form-item label="排序" prop="sort">
              <el-input  v-model="form.sort" placeholder="排序" type="number"  />
            </el-form-item>
            <el-form-item label="字段类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择类型">
                <el-option v-for="item in fieldType" :key="item.value" :label="item.name" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="名称" prop="name">
              <el-input  v-model="form.name" placeholder=""  />
            </el-form-item>
            <el-form-item label="选项" prop="option">
              <div class="optionBox">
                <p v-for="(item,index) in optionArr" :key="index" class="flex">
                  <el-input class="mb_1"  v-model="item.value"  placeholder="例：XXX" />
                  <span class="clear" @click="delOption(index)">清除选项</span>
                </p>
                <el-button type="primary" size="small" @click="addOption()">添加选项</el-button>
              </div>
            </el-form-item>
            <el-form-item label="默认值" prop="default">
              <el-input  v-model="form.default" placeholder="选项英文逗号分隔，例：1,2"  />
            </el-form-item>
            <el-form-item label="提示" prop="tips">
              <el-input  v-model="form.tips" placeholder="小提示" type="textarea"   />
            </el-form-item>
            <el-form-item label="是否筛选" prop="isfilter">
              <el-radio-group v-model="form.isfilter">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="是否排序" prop="isorder">
              <el-radio-group v-model="form.isorder" >
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="状态" prop="isshow">
              <el-radio-group v-model="form.isshow">
                <el-radio :label="1">显示</el-radio>
                <el-radio :label="0">隐藏</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>

          <div class="popBtn">
              <el-button type="" size="small" @click="close()">取消</el-button>
              <el-button v-if="isEdit" type="primary"  size="small" @click="save(formRef)">保存</el-button>
              <el-button v-else type="primary"  size="small" @click="submit(formRef)">确定</el-button>
            </div>
        </div>
      </template>
    </Layer>
  </div>
</template>
<script lang="ts" setup>
import Layer from '@/components/Layer.vue'
import { ref,reactive } from 'vue'
import { artModelItemAddApi,artModelItemUpdateApi } from '@/api/api'
import {type FormInstance ,type FormRules,ElMessage } from 'element-plus'
import { useRoute } from 'vue-router';
const route = useRoute()
let isEdit = ref(false)


const layerRef = ref()
const rules = reactive<FormRules>({
  type: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
  name: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  fieldLabel: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
})

let optionArr = ref([
  {
    value:''
  },
  {
    value:''
  }
])


let form = ref ({
  id:'',
  sort:0,
  type:'',
  name:'',
  default:'',
  option:'',
  tips:'',
  isfilter:0,
  isrequired:0,
  isorder:0,
  isshow:1,
})
const emit = defineEmits(['changeData'])

interface field {
  value:string
  name:string
}

let fieldType:Array<field> = [
  {
    name:'文本',
    value:'text'
  },
  {
    name:'文本域',
    value:'textarea'
  },
  {
    name:'富文本',
    value:'richtext'
  },
  {
    name:'数字',
    value:'number'
  },
  {
    name:'单选',
    value:'radio'
  },
  {
    name:'开关',
    value:'switch'
  },{
    name:'多选',
    value:'checkbox'
  },{
    name:'日期',
    value:'date'
  },{
    name:'日期时间',
    value:'datetime'
  },{
    name:'图片',
    value:'pic'
  },{
    name:'图片集',
    value:'pics'
  },{
    name:'文件',
    value:'file'
  },{
    name:'链接',
    value:'link'
  },
] 

const open = ()=>{
  layerRef.value.showModel = true
}
const close = ()=>{
  form.value = {
    id:'',
    sort:0,
    type:'',
    name:'',
    default:'',
    option:'',
    tips:'',
    isfilter:0,
    isrequired:0,
    isorder:0,
    isshow:1,
  }
  optionArr.value = [
    {
      value:''
    },
    {
      value:''
    }
  ]
  layerRef.value.showModel = false
}


const formRef = ref<FormInstance>()
const submit = (formEl:FormInstance | undefined )=>{
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      console.log(form.value);
      let arr:Array<any> = []
      if(optionArr.value.length > 0){
        optionArr.value.forEach((item:any)=>{
          arr.push(item.value)
        })
      }
      form.value.option = JSON.stringify(arr)
      
      artModelItemAddApi(route.params.modelId,form.value).then((res:any)=>{
        console.log(res);
        if(res.code == 0){
          ElMessage.success('成功')
          emit('changeData')
          close()
        }else{
          ElMessage.error(res.msg)
        }
      })
    }
  })
}

const save = (formEl:FormInstance | undefined)=>{
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      console.log(form.value);
      let arr:Array<any> = []
      if(optionArr.value.length > 0){
        optionArr.value.forEach((item:any)=>{
          arr.push(item.value)
        })
      }
      form.value.option = JSON.stringify(arr)
      artModelItemUpdateApi(route.params.modelId,form.value).then((res:any)=>{
        console.log(res);
        if(res.code == 0){
          ElMessage.success(res.msg)
          emit('changeData')
          close()
        }else{
          ElMessage.error(res.msg)
        }
      })
    }
  })
}

const addOption = ()=>{
  optionArr.value.push({
    value:''
  })
}
const delOption = (index:number)=>{
  optionArr.value.splice(index,1)
}

defineExpose({
  isEdit,
  form,
  open,
  optionArr
})
</script>

<style lang="scss" scoped>
.index{
  width: 50vw;
  height: 520px;
  overflow-y:auto ;
  padding-right: 10px;
}
.optionBox{
  width: 90%;
  p{
    width: 100%;
    .el-input{
      width: 80%;
    }
    .clear{
      color: #409eff;
      cursor: pointer;
    }
  }
}

</style>