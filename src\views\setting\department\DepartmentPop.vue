<template>
  <div>
    <Layer ref="layerRef" :title="isEdit?'修改部门':'添加部门'"  @fatherShow='close'>
      <template #layer>
        <div>
          <el-form ref="formRef" :model="form" :rules="rules" label-width="auto">
            <el-form-item label="排序" prop="sort">
              <el-input  v-model="form.sort" placeholder="排序" type="number"  />
            </el-form-item>
            <el-form-item label="名称" prop="name">
              <el-input  v-model="form.name" placeholder="名称"  />
            </el-form-item>
            <el-form-item label="备注" prop="memo">
              <el-input  v-model="form.memo" placeholder="备注"  />
            </el-form-item>
          </el-form>

          <div class="popBtn">
              <el-button type="" size="small" @click="close(formRef)">取消</el-button>
              <el-button v-if="isEdit" type="primary"  size="small" @click="save(formRef)">保存</el-button>
              <el-button v-else type="primary"  size="small" @click="submit(formRef)">确定</el-button>
            </div>
        </div>
      </template>
    </Layer>
  </div>
</template>
<script lang="ts" setup>
import { ref,reactive } from 'vue'
import Layer from '@/components/Layer.vue'
import { departmentAddApi,departmentUpdateApi } from '@/api/api'
import {type FormInstance ,type FormRules,ElMessage } from 'element-plus'

let isEdit = ref(false)

const layerRef = ref()
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' },
  ],
})
let form = ref ({
  sort:0,
  name:'',
  memo:'',
  pid:0
})
const emit = defineEmits(['changeData'])


const open = ()=>{
  layerRef.value.showModel = true
}
const close = (formEl: FormInstance | undefined)=>{
  if (!formEl) return
  formEl.resetFields()
  layerRef.value.showModel = false
}


const formRef = ref<FormInstance>()
const submit = (formEl:FormInstance | undefined )=>{
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      console.log(form.value);
      departmentAddApi(form.value).then((res:any)=>{
        console.log(res);
        if(res.code == 0){
          ElMessage.success('成功')
          emit('changeData')
          close(formRef.value)
        }else{
          ElMessage.error(res.msg)
        }
      })
    }
  })
}

const save = (formEl:FormInstance | undefined)=>{
  console.log(form.value);
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      console.log(form);
      departmentUpdateApi(form.value).then((res:any)=>{
        console.log(res);
        if(res.code == 0){
          ElMessage.success(res.msg)
          emit('changeData')
          close(formRef.value)
        }else{
          ElMessage.error(res.msg)
        }
      })
    }
  })
}

defineExpose({
  isEdit,
  form,
  open,
})
</script>

<style lang="scss" scoped>


</style>