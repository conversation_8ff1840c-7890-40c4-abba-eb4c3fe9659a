<template>
  <div>
    <div class="searchBox">
      <el-form ref="searchRef" :model="form" class="form">
        <el-form-item prop="name">
          <el-input prefix-icon="Search" v-model="form.name" placeholder="请输入模型名称" />
        </el-form-item>
        <el-form-item prop="status">
          <el-select v-model="form.status" placeholder="请选择状态">
            <template #prefix>
              <el-icon ><Search /></el-icon>
            </template>
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getList">搜索</el-button>
          <span class="clear" @click="resetForm(searchRef)">清除选项</span>
        </el-form-item>
      </el-form>
    </div>
    <div class="toolBox">
      <el-button type="primary" icon="Plus" @click="addModel">添加模型</el-button>
    </div>
    <div>
      <el-table 
        class="table"
        :data="tableData" 
        border 
        style="width: 100%"
        :header-cell-style="{ background: '#F2F2F2', textAlign: 'center' ,color:'#666'}"
        :cell-style="{ textAlign: 'center' }"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="sort" label="排序"  width="60" />
        <el-table-column prop="name" label="模型名称" />
        <el-table-column  label="状态" >
          <template #default="scope">
            {{scope.row.status?'启用':'禁用'}}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="400">
          <template #default="scope" >
            <el-button  type="primary" size="small" plain icon="Setting" @click="handleField(scope.row.id)">字段管理</el-button>
            <!-- <el-button  type="success" size="small" plain icon="View" @click="handleView(scope.row.id)">预览</el-button> -->
            <el-button  type="warning" size="small" plain icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button  type="danger" size="small" plain icon="Delete" @click="handleDel(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <PageCom ref="pageCom" :total="total" @changePage='getList' />
    </div>
    <ModelPop ref="modelPop" @changeData='getList' />
  </div>
</template>

<script lang="ts" setup>

import PageCom from '@/components/PageCom.vue'
import ModelPop from './ModelPop.vue'
import { ref,reactive,onMounted } from 'vue'
import { artModelListApi,artModelRemoveApi } from '@/api/api'
import type { FormInstance } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()
interface RuleForm {
  name: string
  status ?: number 
}

const searchRef = ref<FormInstance>()
const form = reactive<RuleForm>({
  name:'',
})

onMounted(()=>{
  getList()
})


const pageCom = ref()
let total = ref(0)
let tableData = ref([])
const getList = ()=>{
  let data = {
    page:pageCom.value.currentPage,
    rows:pageCom.value.pageSize,
    ...form
  }
  artModelListApi(data).then((res:any) =>{
    console.log('文章模型管理',res)
    if(res.code == 0){
      tableData.value = res.data
      total.value = res.data.length
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  getList()
}

const modelPop = ref()
const addModel = ()=>{
  modelPop.value.open()
  modelPop.value.isEdit = false
}
const handleEdit = (row:msg)=>{
  console.log(row)
  modelPop.value.open()
  modelPop.value.isEdit = true
  modelPop.value.form = JSON.parse(JSON.stringify(row))
}


interface msg{
  id:number,
  status:number
}

const handleDel = (id:number)=>{
  ElMessageBox.confirm('是否删除该数据？','提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'error',
    }
  ).then(() => {
    artModelRemoveApi(id,{}).then((res:any)=>{
      if(res.code == 0){
        ElMessage.success('成功')
        getList()
      }else{
        ElMessage.error(res.msg)
      }
    })
    })
  .catch(() => {
    ElMessage.info('已取消')
  })
}

const handleField = (id:number)=>{
  router.push({
    name:'modelField',
    params:{
      'modelId':id
    }
  })
}

// const fieldView = ref()
// const handleView = (id:number)=>{
//   fieldView.value.open()
//   fieldView.value.getList(id)
// }
</script>