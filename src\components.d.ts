/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ElButton: typeof import('element-plus/es')['ElButton']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElMenuItemGroup: typeof import('element-plus/es')['ElMenuItemGroup']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    HelloWorld: typeof import('./components/HelloWorld.vue')['default']
    IEpAdd: typeof import('~icons/ep/add')['default']
    IEpDocument: typeof import('~icons/ep/document')['default']
    IEpEdit: typeof import('~icons/ep/edit')['default']
    IEpFold: typeof import('~icons/ep/fold')['default']
    IEpLocation: typeof import('~icons/ep/location')['default']
    IEpMenu: typeof import('~icons/ep/menu')['default']
    IEpPlus: typeof import('~icons/ep/plus')['default']
    IEpPosition: typeof import('~icons/ep/position')['default']
    IEpSetting: typeof import('~icons/ep/setting')['default']
    IEpUnlock: typeof import('~icons/ep/unlock')['default']
    IEpUnLock: typeof import('~icons/ep/un-lock')['default']
    IEpUser: typeof import('~icons/ep/user')['default']
    Layer: typeof import('./components/Layer.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
