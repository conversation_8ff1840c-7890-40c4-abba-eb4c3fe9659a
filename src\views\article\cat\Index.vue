<template>
  <div>
    <div class="searchBox">
      <el-form ref="searchRef" :model="form" class="form">
        <el-form-item prop="title">
          <el-input prefix-icon="Search" v-model="form.title" placeholder="请输入分类" />
        </el-form-item>
        <el-form-item prop="type">
          <el-select v-model="form.type" placeholder="请选择类型">
            <template #prefix>
              <el-icon ><Search /></el-icon>
            </template>
            <el-option label="单页" value="page" />
            <el-option label="列表" value="list" />
            <el-option label="外链" value="link" />
          </el-select>
        </el-form-item>
        <el-form-item prop="display">
          <el-select v-model="form.display" placeholder="请选择是否展示">
            <template #prefix>
              <el-icon ><Search /></el-icon>
            </template>
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getList">搜索</el-button>
          <span class="clear" @click="resetForm(searchRef)">清除选项</span>
        </el-form-item>
      </el-form>
    </div>
    <div class="toolBox">
      <el-button type="primary" icon="Plus" @click="addCat">添加分类</el-button>
    </div>
    <div>
      <el-table 
        class="table"
        :data="tableData" 
        border 
        style="width: 100%"
        :header-cell-style="{ background: '#F2F2F2', textAlign: 'center' ,color:'#666'}"
        :cell-style="{ textAlign: 'center' }"
        row-key="id"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="sort" label="排序"  width="60" />
        <el-table-column prop="pid" label="pid"  width="70" />
        <el-table-column prop="title" label="分类名称" show-overflow-tooltip />
        <el-table-column  label="类型">
          <template #default="scope">
            {{scope.row.type == 'page'?'单页':scope.row.type == 'list'?'列表':scope.row.type == 'link'?'外链':''}}
          </template>
        </el-table-column>
        <el-table-column  label="是否展示" >
          <template #default="scope">
            {{scope.row.display?'是':'否'}}
          </template>
        </el-table-column>
        <el-table-column  label="所属模型" prop="modelName"></el-table-column>
        <!-- <el-table-column  label="状态" >
          <template #default="scope">
            {{scope.row.status?'启用':'禁用'}}
          </template>
        </el-table-column> -->
        <el-table-column fixed="right" label="操作" width="300">
          <template #default="scope" >
            <div>
              <el-button v-btn type="primary" size="small" plain icon="Plus" @click="handleAdd(scope.row)">子分类</el-button>
              <el-button v-btn  type="primary" size="small" plain icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button v-btn type="danger" size="small" plain icon="Delete" @click="handleDel(scope.row.id)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <PageCom ref="pageCom" :total="total" @changePage='getList' />
    </div>
    <CatPop ref="catPop" @changeData="getList" />
  </div>
</template>
<script lang="ts" setup>
import PageCom from '@/components/PageCom.vue'
import CatPop from './CatPop.vue'
import { ref,reactive,onMounted } from 'vue'
import { articleCatListApi,articleCatRemoveApi,artModelListApi } from '@/api/api'
import type { FormInstance } from 'element-plus'
// import { useStore } from '@/store/Index'

// const store = useStore()
let total = ref(0)
interface RuleForm {
  title: string
  type: string
  modelId ?: number 
  display ?: number 
}
const searchRef = ref<FormInstance>()
const form = reactive<RuleForm>({
  title:'',
  type:'',
})

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  getList()
}

onMounted(()=>{
  getList()
  getModel()
})

const pageCom = ref()
let tableData = ref([])
const getList = ()=>{
  let data = {
    page:pageCom.value.currentPage,
    rows:pageCom.value.pageSize,
    ...form
  }
  articleCatListApi(data).then((res:any) =>{
    console.log('文章分类管理',res)
    if(res.code == 0){
      tableData.value = res.data
      total.value = res.data.length
    }
  })
}


// 模型
let modelArr = ref([])
const getModel = ()=>{
  artModelListApi({
    page:1,
    rows:999
  }).then((res:any) =>{
    console.log('模型',res)
    if(res.code == 0){
      modelArr.value = res.data
    }
  })
}

interface msg{
  id:number,
  status:boolean
  code:number
}

const catPop = ref()
const addCat = ()=>{
  catPop.value.open()
  catPop.value.isEdit = false
  catPop.value.modelArr = modelArr.value
}
const handleEdit = (row:msg)=>{
  console.log(row)
  catPop.value.open()
  catPop.value.isEdit = true
  catPop.value.modelArr = modelArr.value
  catPop.value.form = JSON.parse(JSON.stringify(row))
}

const handleDel = (id:number)=>{
  console.log(id)
  ElMessageBox.confirm('是否删除该数据？','提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'error',
    }
  ).then(() => {
    articleCatRemoveApi(id,{}).then((res:any)=>{
      console.log('删除',res)
      if(res.code == 0){
        ElMessage.success('成功')
        getList()
      }else{
        ElMessage.error(res.msg)
      }
    })
    })
  .catch(() => {
    ElMessage.info('已取消')
  })
}

const handleAdd = (row:msg)=>{
  console.log(row)
  catPop.value.open()
  catPop.value.isEdit = false
  catPop.value.form.pid = row.id
}

</script>