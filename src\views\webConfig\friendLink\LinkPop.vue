<template>
  <div>
    <Layer ref="layerRef" :title="isEdit?'修改友链':'添加友链'"  @fatherShow='close()'>
      <template #layer>
        <div>
          <el-form ref="formRef" :model="form" :rules="rules" label-width="auto">
            <el-form-item label="排序" prop="sort">
              <el-input  v-model="form.sort" placeholder="排序"  />
            </el-form-item>
            <el-form-item label="名称" prop="name">
              <el-input  v-model="form.name" placeholder="名称"  />
            </el-form-item>
            <el-form-item label="链接" prop="link">
              <el-input  v-model="form.link" placeholder="链接"  />
            </el-form-item>
            <el-form-item label="图片" prop="path">
              <el-upload
                accept=".png,.jpg,.jpeg"
                :file-list="fileList"
                action=""
                :multiple="false"
                :before-upload="beforeUpload"
                :before-remove="beforeRemove"
                :on-change="fileChange"
                :on-exceed="handleExceed"
                :limit="1"
                :with-credentials="true"
              >
                <el-button v-if="!form.path" type="primary" size="small">上传图片</el-button>
                <div v-else>
                  <el-image  style="max-width: 200px;height: 100px"     :src="store.hw + form.path"  fit="fill" />
                  <el-icon @click.stop="form.path = ''"><CircleClose /></el-icon>
                </div>
              </el-upload>
            </el-form-item>
            <el-form-item label="弹出方式" prop="target">
              <el-radio-group v-model="form.target">
                <el-radio :label="1">新页面</el-radio>
                <el-radio :label="0">当前</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option label="启用" :value="1" />
                <el-option label="禁用" :value="0" />
              </el-select>
            </el-form-item>
          </el-form>
          <div class="popBtn">
              <el-button type="" size="small" @click="close()">取消</el-button>
              <el-button v-if="isEdit" type="primary"  size="small" @click="save(formRef)">保存</el-button>
              <el-button v-else type="primary"  size="small" @click="submit(formRef)">确定</el-button>
            </div>
        </div>
      </template>
    </Layer>
  </div>
</template>
<script lang="ts" setup>
import { ref,reactive } from 'vue'
import Layer from '@/components/Layer.vue'
import { linkAddApi,linkUpdateApi,uploadApi } from '@/api/api'
import {type FormInstance ,type FormRules,ElMessage ,type UploadProps, UploadUserFile} from 'element-plus'
import { useStore } from '@/store/Index';

const store = useStore()

let isEdit = ref(false)

interface RuleForm {
  name: string
  path: string
  sort: number
  target: number
  status: number
  link: string
}

const rules = reactive<FormRules<RuleForm>>({
  name: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  link: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
})

let form = ref<RuleForm> ({
  name:'',
  path:'',
  sort:0,
  target:1,
  status:1,
  link:'',
})
const emit = defineEmits(['changeData'])

const layerRef = ref()
const open = ()=>{
  layerRef.value.showModel = true
}
const close = ()=>{
  form.value = {
    name:'',
    path:'',
    sort:0,
    target:1,
    status:1,
    link:'',
  }
  layerRef.value.showModel = false
}


const formRef = ref<FormInstance>()

const submit = (formEl:FormInstance | undefined)=>{
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      console.log(form.value);
      linkAddApi(form.value).then((res:any)=>{
        console.log(res);
        if(res.code == 0){
          ElMessage.success('成功')
          emit('changeData')
          close()
        }else{
          ElMessage.error(res.msg)
        }
      })
    }
  })
}
const save = (formEl:FormInstance | undefined)=>{
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      console.log(form.value);
      linkUpdateApi(form.value).then((res:any)=>{
        console.log(res);
        if(res.code == 0){
          ElMessage.success('成功')
          emit('changeData')
          close()
        }else{
          ElMessage.error(res.msg)
        }
      })
    }
  })
}

// 上传
let fileList = ref<UploadUserFile[]>([])
const handleExceed: UploadProps['onExceed'] = ()=>{
  ElMessage.warning('当前限制选择1个文件')
}
const fileChange: UploadProps['onChange'] = (file)=>{
  fileList.value = [file]
}
const beforeRemove: UploadProps['beforeRemove'] = (file, fileLists)=>{
  console.log(file, fileLists);
  fileList.value  = []
  return true
}

const beforeUpload: UploadProps['beforeUpload'] = (file)=>{
  console.log('文件',file);
  const isLt100M = file.size / 1024 / 1024 < 10;
  if (!isLt100M) {
    ElMessage.warning("大小不得超过10M");
    return false;
  }
  let params = new FormData();
  params.append("file", file);
  params.append("isTemp","forever");
  params.append("uType","admin");
  uploadApi(params).then((res:any)=>{
    console.log(res);
    if(res.code == 0){
      form.value.path =  res.data
    }else{
      ElMessage.error(res.msg)
    }
  })
  return false;
}


defineExpose({
  isEdit,
  form,
  open,
})
</script>

<style lang="scss" scoped>


</style>