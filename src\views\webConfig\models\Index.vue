<template>
  <div class="page-layout-config">
    <div class="page-header">
      <h2>首页布局配置</h2>
      <p class="desc">拖拽调整首页各模块的显示顺序，配置广告位和新闻模块</p>
    </div>

    <div class="layout-container">
      <!-- 左侧：可用模块 -->
      <div class="available-modules">
        <h3>可用模块</h3>

        <!-- 链接位置 -->
        <div class="module-group">
          <h4>链接位置</h4>
          <div class="module-list">
            <div class="module-item entrance-module" @click="addEntrancePosition">
              <el-icon><Picture /></el-icon>
              <span>添加链接位置</span>
              <el-icon><Plus /></el-icon>
            </div>
          </div>
        </div>

        <!-- 轮播模块 -->
        <div class="module-group">
          <h4>轮播模块</h4>
          <div class="module-list">
            <div class="module-item banner-module" @click="addBannerModule">
              <el-icon><Picture /></el-icon>
              <span>添加轮播模块</span>
              <el-icon><Plus /></el-icon>
            </div>
          </div>
        </div>

        <!-- 新闻模块 -->
        <div class="module-group">
          <h4>新闻模块</h4>
          <div class="module-list">
            <div class="module-item news-module" @click="addNewsModule">
              <el-icon><Document /></el-icon>
              <span>添加新闻模块</span>
              <el-icon><Plus /></el-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：页面布局 -->
      <div class="page-layout">
        <h3>页面布局 <span class="layout-count">({{ layoutConfig.layout.length }}个模块)</span></h3>

        <div class="layout-preview">
          <div class="layout-grid">
            <div
              v-for="(positionId, index) in layoutConfig.layout"
              :key="positionId"
              class="layout-item"
              :class="getPositionClass(positionId)"
            >
              <div class="layout-item-header">
                <div class="module-info">
                  <el-icon v-if="getPositionType(positionId) === 'entrance'"><Picture /></el-icon>
                  <el-icon v-else-if="getPositionType(positionId) === 'banner'"><VideoPlay /></el-icon>
                  <el-icon v-else><Document /></el-icon>
                  <span class="module-name">{{ getPositionName(positionId) }}</span>
                  <el-tag
                    :type="getPositionEnabled(positionId) ? 'success' : 'danger'"
                    size="small"
                  >
                    {{ getPositionEnabled(positionId) ? '启用' : '禁用' }}
                  </el-tag>
                  <el-tag
                    :type="getPositionWidth(positionId) === 'full' ? 'info' : 'warning'"
                    size="small"
                  >
                    {{ getPositionWidth(positionId) === 'full' ? '全宽' : '半宽' }}
                  </el-tag>
                </div>
                <div class="module-actions">
                  <el-button size="small" @click="moveUp(index)" :disabled="index === 0">上移</el-button>
                  <el-button size="small" @click="moveDown(index)" :disabled="index === layoutConfig.layout.length - 1">下移</el-button>
                  <el-button size="small" @click="editPosition(positionId)">配置</el-button>
                  <el-button
                    v-if="getPositionType(positionId) === 'entrance'"
                    size="small"
                    type="primary"
                    @click="manageEntranceContent(positionId)"
                  >
                    管理
                  </el-button>
                  <el-button size="small" type="danger" @click="removePosition(index)">删除</el-button>
                </div>
              </div>
              <div class="layout-item-content">
                <div v-if="getPositionType(positionId) === 'news'" class="content-info">
                  <span>分类：{{ getCategoryName(getPositionConfig(positionId).catId) }}</span>
                  <span>数量：{{ getPositionConfig(positionId).limit === -1 ? '全部' : getPositionConfig(positionId).limit }}</span>
                </div>
                <div v-else-if="getPositionType(positionId) === 'banner'" class="content-info">
                  <span>轮播分类：{{ getCategoryName(getPositionConfig(positionId).catId) }}</span>
                  <span>轮播数量：{{ getPositionConfig(positionId).limit === -1 ? '全部' : getPositionConfig(positionId).limit }}</span>
                </div>
                <div v-else class="content-info">
                  <span>链接位置：{{ currentEditPosition || positionId }}</span>
                  <span>链接数量：{{ getEntranceCount(positionId) }}</span>
                </div>
              </div>
            </div>
          </div>

          <div v-if="layoutConfig.layout.length === 0" class="empty-layout">
            <el-empty description="暂无模块，请从左侧添加" />
          </div>
        </div>
      </div>
    </div>

    <!-- 链接管理弹窗 -->
    <EntranceManageDialog ref="entranceManageRef" @refresh="getEntranceData" />

    <!-- 配置弹窗 -->
    <el-dialog
      v-model="configDialogVisible"
      :title="currentEditPosition ? '配置模块' : ''"
      width="600px"
    >
      <div v-if="currentEditPosition && layoutConfig.positions[currentEditPosition]">
        <!-- 链接位置配置 -->
        <div v-if="getPositionType(currentEditPosition) === 'entrance'">
          <el-form label-width="100px">
            <el-form-item label="位置名称">
              <el-input v-model="currentPositionConfig.name" />
            </el-form-item>
            <el-form-item label="启用状态">
              <el-switch v-model="currentPositionConfig.enabled" />
            </el-form-item>
            <el-form-item label="显示宽度">
              <el-radio-group v-model="currentPositionConfig.width">
                <el-radio label="full">全宽（占满一行）</el-radio>
                <el-radio label="half">半宽（一行两列）</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="链接内容">
              <div class="entrance-info">
                <p>该位置共有 {{ getEntranceCount(currentEditPosition) }} 个链接</p>
                <div class="entrance-actions">
                  <el-button type="primary" size="small" @click="manageEntranceContent(currentEditPosition)">
                    <el-icon><Setting /></el-icon>
                    管理链接内容
                  </el-button>
                  <p class="tip">点击管理按钮可直接管理该位置的链接内容</p>
                </div>
              </div>
            </el-form-item>
          </el-form>
        </div>

        <!-- 轮播模块配置 -->
        <div v-else-if="getPositionType(currentEditPosition) === 'banner'">
          <el-form label-width="100px">
            <el-form-item label="模块名称">
              <el-input v-model="currentPositionConfig.name" />
            </el-form-item>
            <el-form-item label="启用状态">
              <el-switch v-model="currentPositionConfig.enabled" />
            </el-form-item>
            <el-form-item label="显示宽度">
              <el-radio-group v-model="currentPositionConfig.width">
                <el-radio label="full">全宽（占满一行）</el-radio>
                <el-radio label="half">半宽（一行两列）</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="轮播分类">
              <el-select v-model="currentPositionConfig.catId" style="width: 100%">
                <el-option
                  v-for="cat in categoryList"
                  :key="cat.id"
                  :label="cat.title"
                  :value="cat.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="轮播数量">
              <el-input-number
                v-model="currentPositionConfig.limit"
                :min="-1"
                :max="50"
              />
              <span class="tip">-1表示显示全部，建议设置具体数量</span>
            </el-form-item>
            <el-form-item label="说明">
              <div class="banner-info">
                <p>轮播模块会自动筛选置顶文章进行轮播显示</p>
                <p class="tip">如果没有置顶文章，将显示该分类下的最新文章</p>
              </div>
            </el-form-item>
          </el-form>
        </div>

        <!-- 新闻模块配置 -->
        <div v-else>
          <el-form label-width="100px">
            <el-form-item label="模块名称">
              <el-input v-model="currentPositionConfig.name" />
            </el-form-item>
            <el-form-item label="启用状态">
              <el-switch v-model="currentPositionConfig.enabled" />
            </el-form-item>
            <el-form-item label="显示宽度">
              <el-radio-group v-model="currentPositionConfig.width">
                <el-radio label="full">全宽（占满一行）</el-radio>
                <el-radio label="half">半宽（一行两列）</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="文章分类">
              <el-select v-model="currentPositionConfig.catId" style="width: 100%">
                <el-option
                  v-for="cat in categoryList"
                  :key="cat.id"
                  :label="cat.title"
                  :value="cat.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="显示数量">
              <el-input-number
                v-model="currentPositionConfig.limit"
                :min="-1"
                :max="50"
              />
              <span class="tip">-1表示显示全部</span>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <template #footer>
        <el-button @click="configDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="savePositionConfig">确定</el-button>
      </template>
    </el-dialog>

    <div class="action-bar">
      <el-button type="primary" size="large" @click="saveConfig" :loading="saving">保存配置</el-button>
      <el-button size="large" @click="resetConfig">重置</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { articleCatListApi, comConfigFindApi, comConfindSaveApi, entrancePosApi, bannersApi, bannersRemoveByPosApi } from '@/api/api'
import { computed } from 'vue'
import { ElMessageBox } from 'element-plus'
import EntranceManageDialog from './EntranceManageDialog.vue'

interface Category {
  id: number
  title: string
  children?: Category[]
}

// 移除不再需要的EntrancePosition接口

interface PositionConfig {
  type: 'entrance' | 'news' | 'banner'
  enabled: boolean
  name: string
  width: 'full' | 'half'
  catId?: number
  limit?: number
}

interface LayoutConfig {
  positions: Record<string, PositionConfig>
  layout: string[]
}

const categoryList = ref<Category[]>([])
const entranceData = ref<any[]>([])
const saving = ref(false)
const configDialogVisible = ref(false)
const currentEditPosition = ref<string>('')
const entranceManageRef = ref()

// 页面布局配置
const layoutConfig = reactive<LayoutConfig>({
  positions: {},
  layout: []
})

// 移除不再需要的availableEntrancePositions计算属性

// 当前编辑位置的配置
const currentPositionConfig = computed(() => {
  if (!currentEditPosition.value || !layoutConfig.positions[currentEditPosition.value]) {
    return {
      type: 'news',
      enabled: true,
      name: '',
      width: 'full',
      catId: 1,
      limit: 4
    }
  }
  return layoutConfig.positions[currentEditPosition.value]
})

// 获取分类列表
const getCategoryList = async () => {
  try {
    const res: any = await articleCatListApi({ page: 1, rows: 999 })
    if (res.code === 0) {
      categoryList.value = flattenCategories(res.data)
    }
  } catch (error) {
    ElMessage.error('获取分类列表失败')
  }
}

// 获取广告数据
const getEntranceData = async () => {
  try {
    const res: any = await bannersApi({ page: 1, rows: 999 })
    if (res.code === 0) {
      entranceData.value = res.data
    }
  } catch (error) {
    console.log('获取广告数据失败')
  }
}

// 扁平化分类树
const flattenCategories = (categories: Category[]): Category[] => {
  const result: Category[] = []
  const flatten = (cats: Category[], prefix = '') => {
    cats.forEach(cat => {
      result.push({
        id: cat.id,
        title: prefix + cat.title
      })
      if (cat.children && cat.children.length > 0) {
        flatten(cat.children, prefix + '　')
      }
    })
  }
  flatten(categories)
  return result
}

// 加载配置
const loadConfig = async () => {
  try {
    const res: any = await comConfigFindApi({ type: 'page_layout' })
    if (res.code === 0 && res.data) {
      // 适配现有接口格式，从page_layout字段中获取配置
      const configData = res.data.page_layout
      if (configData) {
        const config = JSON.parse(configData)
        if (config.positions) {
          // 确保所有位置都有width字段（向后兼容）
          Object.keys(config.positions).forEach(key => {
            if (!config.positions[key].width) {
              config.positions[key].width = 'full'
            }
          })
          layoutConfig.positions = config.positions
        }
        if (config.layout) {
          layoutConfig.layout = config.layout
        }
      }
    }
  } catch (error) {
    console.log('首次加载，使用默认配置')
    // 设置默认配置
    layoutConfig.positions = {
      'index_1': {
        type: 'entrance',
        enabled: true,
        name: '链接位置 index_1',
        width: 'full'
      },
      'banner_main': {
        type: 'banner',
        enabled: true,
        name: '主轮播',
        width: 'full',
        catId: 1,
        limit: 5
      },
      'news_module_1': {
        type: 'news',
        enabled: true,
        name: '新闻模块',
        width: 'full',
        catId: 1,
        limit: 4
      }
    }
    layoutConfig.layout = ['index_1', 'banner_main', 'news_module_1']
  }
}

// 生成新的链接位置键
const generateEntrancePositionKey = () => {
  const existingKeys = Object.keys(layoutConfig.positions)
    .filter(key => key.startsWith('index_'))
    .map(key => {
      const match = key.match(/index_(\d+)/)
      return match ? parseInt(match[1]) : 0
    })

  const maxIndex = existingKeys.length > 0 ? Math.max(...existingKeys) : 0
  return `index_${maxIndex + 1}`
}

// 添加链接位置到布局
const addEntrancePosition = () => {
  const posKey = generateEntrancePositionKey()

  // 添加位置配置
  layoutConfig.positions[posKey] = {
    type: 'entrance',
    enabled: true,
    name: `链接位置 ${posKey}`,
    width: 'full'
  }

  // 添加到布局
  layoutConfig.layout.push(posKey)
  ElMessage.success(`已添加链接位置：${posKey}`)
}

// 添加轮播模块
const addBannerModule = () => {
  const moduleId = `banner_module_${Date.now()}`

  // 添加位置配置
  layoutConfig.positions[moduleId] = {
    type: 'banner',
    enabled: true,
    name: '轮播模块',
    width: 'full',
    catId: categoryList.value[0]?.id || 1,
    limit: 5
  }

  // 添加到布局
  layoutConfig.layout.push(moduleId)
  ElMessage.success('轮播模块已添加')
}

// 添加新闻模块
const addNewsModule = () => {
  const moduleId = `news_module_${Date.now()}`

  // 添加位置配置
  layoutConfig.positions[moduleId] = {
    type: 'news',
    enabled: true,
    name: '新闻模块',
    width: 'full',
    catId: categoryList.value[0]?.id || 1,
    limit: 4
  }

  // 添加到布局
  layoutConfig.layout.push(moduleId)
  ElMessage.success('新闻模块已添加')
}

// 删除位置
const removePosition = async (index: number) => {
  if (index < 0 || index >= layoutConfig.layout.length) {
    ElMessage.error('无效的位置索引')
    return
  }

  const positionId = layoutConfig.layout[index]
  const position = layoutConfig.positions[positionId]

  if (!position) {
    ElMessage.error('位置配置不存在')
    return
  }

  // 如果是链接位置，需要特殊处理
  if (position.type === 'entrance') {
    const entranceCount = getEntranceCount(positionId)

    let confirmMessage = `确定要删除链接位置"${position.name}"吗？`
    if (entranceCount > 0) {
      confirmMessage += `\n\n⚠️ 注意：该位置下有 ${entranceCount} 个链接，删除位置时会同步删除所有链接内容！`
    }

    try {
      await ElMessageBox.confirm(confirmMessage, '删除确认', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      })

      // 如果有广告，先删除该位置的所有广告
      if (entranceCount > 0) {
        try {
          const res: any = await bannersRemoveByPosApi({ pos: positionId })
          if (res.code === 0) {
            ElMessage.success(`已删除位置 ${positionId} 下的 ${entranceCount} 个链接`)
          } else {
            ElMessage.warning('广告删除可能不完整，请检查')
          }
        } catch (error) {
          ElMessage.error('删除位置链接失败')
          return
        }
      }

      // 删除位置配置
      delete layoutConfig.positions[positionId]
      layoutConfig.layout.splice(index, 1)

      // 刷新链接数据
      getEntranceData()

      ElMessage.success('链接位置已删除')
    } catch (error) {
      // 用户取消删除
    }
  } else {
    // 非广告位置，直接删除
    try {
      await ElMessageBox.confirm(`确定要删除"${position.name}"吗？`, '删除确认', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      })

      delete layoutConfig.positions[positionId]
      layoutConfig.layout.splice(index, 1)
      ElMessage.success('模块已删除')
    } catch (error) {
      // 用户取消删除
    }
  }
}

// 编辑位置
const editPosition = (positionId: string) => {
  if (!layoutConfig.positions[positionId]) {
    ElMessage.error('配置不存在')
    return
  }
  currentEditPosition.value = positionId
  configDialogVisible.value = true
}

// 保存位置配置
const savePositionConfig = () => {
  if (currentEditPosition.value && layoutConfig.positions[currentEditPosition.value]) {
    // 配置已经通过 v-model 自动更新到 layoutConfig.positions 中
    ElMessage.success('配置已更新')
  }
  configDialogVisible.value = false
  currentEditPosition.value = ''
}

// 上移模块
const moveUp = (index: number) => {
  if (index > 0) {
    const temp = layoutConfig.layout[index]
    layoutConfig.layout[index] = layoutConfig.layout[index - 1]
    layoutConfig.layout[index - 1] = temp
  }
}

// 下移模块
const moveDown = (index: number) => {
  if (index < layoutConfig.layout.length - 1) {
    const temp = layoutConfig.layout[index]
    layoutConfig.layout[index] = layoutConfig.layout[index + 1]
    layoutConfig.layout[index + 1] = temp
  }
}

// 管理链接内容
const manageEntranceContent = (positionId: string) => {
  const positionName = getPositionName(positionId)
  entranceManageRef.value?.open(positionId, positionName)
}

// 工具方法
const getPositionType = (positionId: string) => {
  return layoutConfig.positions[positionId]?.type || 'news'
}

const getPositionName = (positionId: string) => {
  return layoutConfig.positions[positionId]?.name || positionId
}

const getPositionEnabled = (positionId: string) => {
  return layoutConfig.positions[positionId]?.enabled || false
}

const getPositionConfig = (positionId: string) => {
  return layoutConfig.positions[positionId] || {
    type: 'news',
    enabled: false,
    name: '',
    width: 'full',
    catId: 1,
    limit: 4
  }
}

const getPositionWidth = (positionId: string) => {
  return layoutConfig.positions[positionId]?.width || 'full'
}

const getPositionClass = (positionId: string) => {
  const type = getPositionType(positionId)
  const enabled = getPositionEnabled(positionId)
  const width = getPositionWidth(positionId)
  return {
    'entrance-position': type === 'entrance',
    'news-position': type === 'news',
    'banner-position': type === 'banner',
    'disabled': !enabled,
    'full-width': width === 'full',
    'half-width': width === 'half'
  }
}

const getCategoryName = (catId: number | undefined) => {
  if (!catId) return '未选择分类'
  const category = categoryList.value.find(cat => cat.id === catId)
  return category?.title || '未知分类'
}

const getEntranceCount = (posKey: string) => {
  return entranceData.value.filter(item => item.pos === posKey).length
}

// 保存配置
const saveConfig = async () => {
  saving.value = true
  try {
    const config = {
      positions: layoutConfig.positions,
      layout: layoutConfig.layout
    }

    // 适配现有保存接口格式
    const saveData = {
      page_layout: JSON.stringify(config)
    }

    const res: any = await comConfindSaveApi({
      type: 'page_layout',
      data: JSON.stringify(saveData)
    })

    if (res.code === 0) {
      ElMessage.success('配置保存成功')
    } else {
      ElMessage.error(res.msg || '保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 重置配置
const resetConfig = () => {
  layoutConfig.positions = {
    'index_1': {
      type: 'entrance',
      enabled: true,
      name: '链接位置 index_1',
      width: 'full'
    },
    'banner_main': {
      type: 'banner',
      enabled: true,
      name: '主轮播',
      width: 'full',
      catId: 1,
      limit: 5
    },
    'news_module_1': {
      type: 'news',
      enabled: true,
      name: '新闻模块',
      width: 'full',
      catId: 1,
      limit: 4
    }
  }
  layoutConfig.layout = ['index_1', 'banner_main', 'news_module_1']
}

onMounted(() => {
  getCategoryList()
  getEntranceData()
  loadConfig()
})
</script>

<style scoped>
.page-layout-config {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.desc {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.layout-container {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.available-modules {
  width: 300px;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.available-modules h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
}

.module-group {
  margin-bottom: 20px;
}

.module-group h4 {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
}

.module-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.module-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.module-item:hover {
  border-color: #409eff;
  background: #ecf5ff;
}

.entrance-module {
  border-left: 4px solid #67c23a;
}

.banner-module {
  border-left: 4px solid #e6a23c;
}

.news-module {
  border-left: 4px solid #409eff;
}

.page-layout {
  flex: 1;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
}

.page-layout h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
}

.layout-count {
  color: #909399;
  font-size: 14px;
  font-weight: normal;
}

.layout-preview {
  min-height: 400px;
}

.layout-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  align-items: start;
}

.layout-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.layout-item {
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s;
}

.layout-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.layout-item.entrance-position {
  border-left: 4px solid #67c23a;
}

.layout-item.news-position {
  border-left: 4px solid #409eff;
}

.layout-item.banner-position {
  border-left: 4px solid #e6a23c;
}

.layout-item.disabled {
  opacity: 0.6;
  background: #f5f5f5;
}

.layout-item.full-width {
  grid-column: 1 / -1;
}

.layout-item.half-width {
  grid-column: span 1;
}

.layout-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.module-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.module-name {
  font-weight: 500;
  color: #303133;
}

.module-actions {
  display: flex;
  gap: 8px;
}

.layout-item-content {
  color: #606266;
  font-size: 14px;
}

.content-info {
  background: #f0f0f0;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.content-info span {
  display: block;
}

.empty-layout {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.entrance-info {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
}

.entrance-info p {
  margin: 0 0 8px 0;
}

.entrance-info .tip {
  color: #909399;
  font-size: 12px;
  margin: 0;
}

.entrance-actions {
  margin-top: 12px;
}

.entrance-actions .tip {
  margin-top: 8px;
  color: #909399;
  font-size: 12px;
}

.tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.action-bar {
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #e4e7ed;
  margin-top: 20px;
}
</style>
