<template>
  <div>
    <Layer ref="layerRef" title="预览"  @fatherShow='close'>
      <template #layer>
        <div class="index">
          <el-form ref="formRef" :model="form" :rules="rules" label-width="auto">
            <el-form-item v-for="(item,index) in fieldArr" :key="item.id"  :label="item.fieldLabel" >
              <el-input v-if="item.fieldType == 'text'"  v-model="item.defaultValue"  />
              <el-input v-if="item.fieldType == 'multiText'"  v-model="item.defaultValue"  />
              <el-radio-group v-if="item.fieldType == 'radio'" v-model="item.defaultValue">
                <el-radio v-for="(child) in item.fieldValues" :key="child.value" :label="child.value">{{child.name}}</el-radio>
              </el-radio-group>
              <el-checkbox-group v-if="item.fieldType == 'check'" v-model="item.defaultValue">
                <el-checkbox v-for="(child) in item.fieldValues" :key="child.value" :label="child.value" >{{child.name}}</el-checkbox>
              </el-checkbox-group>
              <el-select v-if="item.fieldType == 'select'" v-model="item.defaultValue" >
                <el-option
                  v-for="(child) in item.fieldValues"
                  :key="child.value"
                  :label="child.name"
                  :value="child.value"
                />
              </el-select>
              <el-select v-if="item.fieldType == 'multiSelect'" v-model="item.defaultValue" multiple>
                <el-option
                  v-for="(child) in item.fieldValues"
                  :key="child.value"
                  :label="child.name"
                  :value="child.value"
                />
              </el-select>
              <el-upload
                v-if="item.fieldType == 'picture'"
                accept=".png,.jpg,.jpeg"
                action=""
                :multiple="false"
                :before-upload="beforeUpload1"
                :before-remove="beforeRemove1"
                :on-change="fileChange1"
                :on-exceed="handleExceed"
                :limit="1"
                :with-credentials="true"
              >
                <el-button v-if="!item.defaultValue" type="primary" size="small" @click="uploadImg(index)">上传</el-button>
                <el-image  v-else style="height: 100px" :src="store.hw + item.defaultValue" />
              </el-upload>
              <div v-if="item.fieldType == 'multiPicture'">
                <el-upload
                  accept=".png,.jpg,.jpeg"
                  action=""
                  :before-upload="beforeUpload"
                  :show-file-list = 'false'
                >
                  
                  <el-button  type="primary" size="small" @click="uploadImg(index)">上传</el-button>
                </el-upload>
                <div v-if="item.defaultValue.length > 0">
                  <el-image  v-for="(img,imgIndex) in item.defaultValue" :key="img" style="height: 100px" :src="store.hw + img" @click="delImg(imgIndex)" />
                </div>
              </div>
              <WangEditor v-if="item.fieldType == 'richText'"  :initValue="item.defaultValue" @getEditorContent="onEditorChange" />
              <el-input v-if="item.fieldType == 'number'"  v-model="item.defaultValue" type="number"  />
              <el-date-picker
                v-if="item.fieldType == 'date'"
                v-model="item.defaultValue"
                type="date"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
              <el-date-picker
                v-if="item.fieldType == 'datetime'"
                v-model="item.defaultValue"
                type="datetime"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
              <el-upload
                v-if="item.fieldType == 'file'"
                accept=""
                action=""
                :multiple="false"
                :before-upload="beforeUpload1"
                :on-exceed="handleExceed"
                :limit="1"
                :with-credentials="true"
              >
                <el-button v-if="!item.defaultValue" type="primary" size="small" @click="uploadImg(index)">上传文件</el-button>
                <a v-else :href="store.hw + item.defaultValue">文件</a>
              </el-upload>

            </el-form-item>
          </el-form>

          <div class="popBtn">
              <el-button type="" size="small" @click="close()">取消</el-button>
              <el-button type="primary"  size="small" @click="submit(formRef)">确定</el-button>
            </div>
        </div>
      </template>
    </Layer>
  </div>
</template>
<script lang="ts" setup>
import WangEditor from '@/components/WangEditor.vue'
import Layer from '@/components/Layer.vue'
import { ref,reactive } from 'vue'
import { artModelItemListApi,artModelItemAddApi,uploadApi } from '@/api/api'
import {type FormInstance ,type FormRules,ElMessage,type UploadProps,UploadUserFile } from 'element-plus'
import { useRoute } from 'vue-router';
const route = useRoute()
import { useStore } from '@/store/Index'
const store = useStore()
const layerRef = ref()
const rules = reactive<FormRules>({
  fieldType: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
  fieldCode: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  fieldLabel: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
})
let form = ref ({
  sort:0,
  fieldType:'',
  fieldCode:'',
  fieldLabel:'',
  length:100,
  defaultValue:'',
  rules:'',
  tips:'',
  errorMsg:'',
  fieldValues:'',
  isFilter:false,
  isSort:false,
  status:1,
})
const emit = defineEmits(['changeData'])


const open = ()=>{
  layerRef.value.showModel = true
}
const close = ()=>{
  form.value = {
    sort:0,
    fieldType:'',
    fieldCode:'',
    fieldLabel:'',
    length:100,
    defaultValue:'',
    rules:'',
    tips:'',
    errorMsg:'',
    fieldValues:'',
    isFilter:false,
    isSort:false,
    status:1,
  }
  console.log(form.value)
  layerRef.value.showModel = false
}
interface fieldValues {
  name:string
  value:string
}
interface  field {
  id:number
  fieldLabel:string
  fieldValues:Array<fieldValues>
  fieldType:string
  defaultValue:any
}

let fieldArr = ref<Array<field>>([])
const getList= (modelId:number)=>{
  artModelItemListApi(modelId,{
    page:1
  }).then((res:any) =>{
    console.log('模型字段',res)
    if(res.code == 0){
      res.data.forEach((item:any)=>{
        if(item.fieldType == 'check' || item.fieldType == 'multiSelect' || item.fieldType == 'multiPicture'){
          console.log(item.defaultValue)
          if(item.defaultValue){
            item.defaultValue = item.defaultValue.indexOf('|')?item.defaultValue = item.defaultValue.split('|'):[item.defaultValue]
          }else{
            item.defaultValue = []
          }
        }
        if(item.fieldValues){
          console.log(item.fieldValues.split('\n'))
          let arr:any = []
          item.fieldValues.split('\n').forEach((item:any)=>{
            arr.push({
              name:item.split('|')[0],
              value:item.split('|')[1]
            })
          })
          item.fieldValues = arr
        }
        console.log(item.fieldType,item.fieldValues,item.defaultValue)
      })
      fieldArr.value = res.data
    }
  })
}

const formRef = ref<FormInstance>()
const submit = (formEl:FormInstance | undefined )=>{
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      console.log(form.value);
      artModelItemAddApi(route.params.modelId,{
        ...form.value,
      }).then((res:any)=>{
        console.log(res);
        if(res.code == 0){
          ElMessage.success('成功')
          emit('changeData')
          close()
        }else{
          ElMessage.error(res.msg)
        }
      })
    }
  })
}

let fileList1 = ref<UploadUserFile[]>([])
const handleExceed: UploadProps['onExceed'] = ()=>{
  ElMessage.warning('当前限制选择1个文件')
}
const fileChange1: UploadProps['onChange'] = (file)=>{
  fileList1.value = [file]
}

const beforeRemove1: UploadProps['beforeRemove'] = (file, fileLists)=>{
  console.log(file, fileLists);
  fileList1.value  = []
  return true
}

const beforeUpload1: UploadProps['beforeUpload'] = (file)=>{
  console.log('文件',file);
  const isLt100M = file.size / 1024 / 1024 < 10;
  if (!isLt100M) {
    ElMessage.warning("大小不得超过10M");
    return false;
  }
  let params = new FormData();
  params.append("file", file);
  params.append("isTemp","forever");
  params.append("uType","admin");
  uploadApi(params).then((res:any)=>{
    console.log(res);
    if(res.code == 0){
      fieldArr.value[uploadIndex.value].defaultValue = res.data
    }else{
      ElMessage.error(res.msg)
    }
  })
  return false;
}

let uploadIndex = ref(0)
const uploadImg = (index:number)=>{
  uploadIndex.value = index
  fieldArr.value[uploadIndex.value]
}


const beforeUpload: UploadProps['beforeUpload'] = (file)=>{
  console.log('文件',file);
  const isLt100M = file.size / 1024 / 1024 < 10;
  if (!isLt100M) {
    ElMessage.warning("大小不得超过10M");
    return false;
  }
  let params = new FormData();
  params.append("file", file);
  params.append("isTemp","forever");
  params.append("uType","admin");
  uploadApi(params).then((res:any)=>{
    console.log(res);
    if(res.code == 0){
      fieldArr.value[uploadIndex.value].defaultValue.push(res.data)
    }else{
      ElMessage.error(res.msg)
    }
  })
  return false;
}

const delImg = (index:number)=>{
  fieldArr.value[uploadIndex.value].defaultValue.splice(index,1)
}

const onEditorChange = (arr:any, html:string)=>{
  console.log(arr, html)
}

defineExpose({
  form,
  open,
  getList
})
</script>

<style lang="scss" scoped>
.index{
  width: 800px;
  height: 500px;
  overflow-y:auto ;
  padding-right: 10px;
}

</style>