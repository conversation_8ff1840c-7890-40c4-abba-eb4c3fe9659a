import service from "./index";

/**
 * 公共请求
 * @param {*} url
 * @param {*} data
 */
export const _axios = (url:string, data:object, method:string) => {
  data = {
    ...data,
    uType:'admin'
  }
  console.log('后台请求数据',method,data)
    if (method == "post" || method == "put"  || method == "delete" ) {
      return new Promise((resolve, reject) => {
        service({
          method,
          url,
          data
        }).then(res => {
          resolve(res.data);
        }).catch(err => {
          reject(err);
        });
      });
    }else if(method == 'get'){
      return new Promise((resolve, reject) => {
        service({
          url,
          method,
          params:data,
        }).then(res => {
          resolve(res.data);
        }).catch(err => {
          reject(err);
        });
      });
    }else{
      return Promise.reject()
    }
};

// 上传
export const _axiosUpload = (url:string, data:object, method:string) => {
  console.log('后台上传',data)
  return new Promise((resolve, reject) => {
    service({
      method,
      url,
      data,
      headers: {
        'Content-Type':'multipart/form-data'
      }
    }).then(res => {
      resolve(res.data);
    })
    .catch(err => {
      reject(err);
    });
  });
};