/**
 * @description image menu entry
 * <AUTHOR>
 */

import ImageWidth60 from './Width60'
import ImageWidth70 from './Width70'
import ImageWidth80 from './Width80'

export const imageWidth60MenuConf = {
  key: 'imageWidth60',
  factory() {
    return new ImageWidth60()
  },
}

export const imageWidth70MenuConf = {
  key: 'imageWidth70',
  factory() {
    return new ImageWidth70()
  },
}

export const imageWidth80MenuConf = {
  key: 'imageWidth80',
  factory() {
    return new ImageWidth80()
  },
}
