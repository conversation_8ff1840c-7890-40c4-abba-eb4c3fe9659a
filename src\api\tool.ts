// 验证码codeID
export function uuid() {
  var chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
  var uuid = [], i;
  var r;
  uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
  uuid[14] = '4';
  for (i = 0; i < 36; i++) {
    if (!uuid[i]) {
      r = 0 | Math.random()*16;
      uuid[i] = chars[(i == 19) ? (r & 0x3) | 0x8 : r];
    }
  }
  
  return uuid.join('');
}

// 时间转化 2020-7-9
export const getTime = (data:string) => {
  let d = new Date(data);
  // let datetime=d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate() + ' ' + d.getHours() + ':' + d.getMinutes() + ':' + d.getSeconds();
  let datatime = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
  return datatime
}

// fmt (yyyy-MM-dd hh:mm:ss,time 时间戳)
export const getDate = (fmt:string,time:any) => {
  const date = new Date(time)
  var o:any = {
    "M+": date.getMonth() + 1, //月份 
    "d+": date.getDate(), //日 
    "h+": date.getHours(), //小时 
    "m+": date.getMinutes(), //分 
    "s+": date.getSeconds() //秒 
  };
  if (/(y+)/.test(fmt)){ //根据y的长度来截取年
  fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
  }
  for (var k in o){
    if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
  }

  return fmt
}

//处理秒变成时分秒
export const secondsFormat = (s:number)=>{  //2:24:36  8676
  let hour = Math.floor(s / 3600);
  let minute = Math.floor((s - hour*3600)/60);
  let second = Math.floor(s - hour*3600 - minute*60);
  if(hour){
    if(minute < 10){
      if(second < 10){
        return hour+":0"+minute+":0"+second;
      }else{
        return hour+":0"+minute+":"+second;
      }
    }else{
      if(second < 10){
        return hour+":"+minute+":0"+second;
      }else{
        return hour+":"+minute+":"+second;
      }
    }
  }else{
    if(minute < 10){
      if(second < 10){
        return "00:0"+minute+":0"+second;
      }else{
        return "00:0"+minute+":"+second;
      }
    }else{
      if(second < 10){
        return "00:"+minute+":0"+second;
      }else{
        return "00:"+minute+":"+second;
      }
    }
  }
}