<template>
  <div>
    <Layer ref="layerRef" title="修改信息"  @fatherShow='close'>
      <template #layer>
        <div>
          <el-form ref="formRef" :model="form" :rules="rules" label-width="auto">
            <el-form-item label="昵称" prop="nicker">
              <el-input  v-model="form.nicker" placeholder="昵称"  />
            </el-form-item>
            <el-form-item label="手机" prop="phone">
              <el-input  v-model="form.phone" placeholder="手机"  />
            </el-form-item>
            <el-form-item label="邮箱" prop="email">
              <el-input  v-model="form.email" placeholder="邮箱"  />
            </el-form-item>
            <el-form-item label="头像" prop="avatar">
              <el-upload
                accept=".png,.jpg,.jpeg"
                :file-list="fileList"
                action=""
                :multiple="false"
                :before-upload="beforeUpload"
                :before-remove="beforeRemove"
                :on-change="fileChange"
                :on-exceed="handleExceed"
                :limit="1"
                :with-credentials="true"
              >
                <el-button v-if="!form.avatar" type="primary" size="small">上传头像</el-button>
                <el-avatar v-else :size="50" :src="store.hw + form.avatar" />
              </el-upload>
            </el-form-item>
          </el-form>
          <div class="popBtn">
              <el-button type="" size="small" @click="close(formRef)">取消</el-button>
              <el-button type="primary"  size="small" @click="save(formRef)">保存</el-button>
            </div>
        </div>
      </template>
    </Layer>
  </div>
</template>
<script lang="ts" setup>
import { ref,reactive } from 'vue'
import Layer from '@/components/Layer.vue'
import {adminChangeSelfApi,uploadApi,adminInfoApi } from '@/api/api'
import {type FormInstance ,type FormRules,ElMessage ,type UploadProps, UploadUserFile} from 'element-plus'
import { useStore } from '@/store/Index';

const store = useStore()

interface RuleForm {
  nicker: string
  avatar: string
  email: string
  phone: string
}

const rules = reactive<FormRules<RuleForm>>({
  nicker: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
})

let form = ref<RuleForm> ({
  nicker:'',
  avatar:'',
  email:'',
  phone:''
})

const layerRef = ref()
const open = ()=>{
  layerRef.value.showModel = true
}
const close = (formEl: FormInstance | undefined)=>{
  if (!formEl) return
  formEl.resetFields()
  layerRef.value.showModel = false
}


const formRef = ref<FormInstance>()
const save = (formEl:FormInstance | undefined)=>{
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      console.log(form.value);
      adminChangeSelfApi(form.value).then((res:any)=>{
        console.log(res);
        if(res.code == 0){
          ElMessage.success('成功')
          getDetail(store.userInfo.id)
          close(formRef.value)
        }else{
          ElMessage.error(res.msg)
        }
      })
    }
  })
}

const getDetail = (id:number)=>{
  adminInfoApi(id).then((res:any)=>{
    console.log(res);
    if(res.code == 0){
      store.changeUser(res.data)
    }else{
      ElMessage.error(res.msg)
    }
  })
}

// 上传
let fileList = ref<UploadUserFile[]>([])
const handleExceed: UploadProps['onExceed'] = ()=>{
  ElMessage.warning('当前限制选择1个文件')
}
const fileChange: UploadProps['onChange'] = (file)=>{
  fileList.value = [file]
}
const beforeRemove: UploadProps['beforeRemove'] = (file, fileLists)=>{
  console.log(file, fileLists);
  fileList.value  = []
  return true
}

const beforeUpload: UploadProps['beforeUpload'] = (file)=>{
  console.log('文件',file);
  const isLt100M = file.size / 1024 / 1024 < 10;
  if (!isLt100M) {
    ElMessage.warning("大小不得超过10M");
    return false;
  }
  let params = new FormData();
  params.append("file", file);
  params.append("isTemp","forever");
  params.append("uType","admin");
  uploadApi(params).then((res:any)=>{
    console.log(res);
    if(res.code == 0){
      form.value.avatar =  res.data
    }else{
      ElMessage.error(res.msg)
    }
  })
  return false;
}


defineExpose({
  form,
  open,
})
</script>

<style lang="scss" scoped>


</style>