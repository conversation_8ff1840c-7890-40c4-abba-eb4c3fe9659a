<template>
  <div class="home">
    <div class="menu">
      <h3 v-show="!isCollapse">CMS管理系统</h3>
      <h3 v-show="isCollapse">CMS</h3>
      <el-menu
        :default-active="router.currentRoute.value.fullPath"
        :collapse="isCollapse"
        @open="handleOpen"
        @close="handleClose"
        active-text-color="#ffd04b"
        background-color="#545c64"
        text-color="#fff"
        :router="true"
      > 
        <div v-for="(item,index) in menuArr" :key="index">
          <el-sub-menu v-if="item.children && item.children.length > 0" :index="String(item.id)"  >
            <template #title>
              <el-icon>
                <component :is="item.icon"></component>
              </el-icon>
              <span>{{ item.name }}</span>
            </template>
            <div v-if="item.children && item.children.length > 0">
              <el-menu-item-group v-for="(child,childIndex) in item.children" :key="childIndex" >
                <el-menu-item :index="child.url" @click="jumpPath(child)">{{child.name}}</el-menu-item>
              </el-menu-item-group>
            </div>
          </el-sub-menu>
          <el-menu-item v-else :index="item.url"  @click="jumpPath(item)">
            <template #title>
              <el-icon>
                <component :is="item.icon"></component>
              </el-icon>
              <span>{{ item.name }}</span>
            </template>
          </el-menu-item>
        </div>
        
      </el-menu>
    </div>
    <div class="contentBox ">
      <header>
        <div class="header_left flex_center">
          <el-icon class="icon" size="18" @click="changeMenu"><Fold /></el-icon>
          <el-scrollbar class="scrollBox" ref="scrollbar">
            <div class="nav">
              <router-link to="/home">控制台</router-link>
              <router-link v-for="(item,index) in routerArr" :key="index" :to="item.path" @click="jumpNav(index)">
                <span>{{item.name}}</span>
                <el-icon class="icon" @click.prevent="closeRouter(index)"><CircleCloseFilled /></el-icon>
              </router-link>
            </div>
          </el-scrollbar>
        </div>
        <el-dropdown class="headBox">
          <span class="el-dropdown-link">
            {{store.userInfo.name?store.userInfo.name:''}}
            <el-image class="img" :src="head" fit="fill" />
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="changeSelf">修改信息</el-dropdown-item>
              <el-dropdown-item @click="changePwd">修改密码</el-dropdown-item>
              <el-dropdown-item @click="loginOut">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </header>
      <div class="content">
        <div class="fff">
          <router-view></router-view>
        </div>
      </div>
    </div>
    <ChangePwd ref="changepwd" />
    <ChangeMsg ref="changemsg" />
  </div>
</template>

<script lang="ts" setup>
import ChangePwd from '@/components/ChangePwd.vue'
import ChangeMsg from '@/components/ChangeMsg.vue'
import head from '@/assets/head.png'
import { ref,onMounted } from 'vue'
import { useStore } from '@/store/Index'
import { adminMenusApi,logoutApi } from '@/api/api'
import { useRouter } from 'vue-router'
const router = useRouter()
const store = useStore()

onMounted(()=>{
  getMenu()
  console.log(store.routerArr,'数组')
  if(store.routerArr.length > 0){
    routerArr.value = store.routerArr
  }
})



let menuArr = ref<Array<any>>([])
const getMenu = ()=>{
  adminMenusApi({}).then((res:any)=>{
    console.log('菜单权限',res)
    if(res.code == 0){
      menuArr.value = res.data
    }
  })
}

let isCollapse = ref(false)
const changeMenu = ()=>{
  isCollapse.value = !isCollapse.value
}

const handleOpen = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}
const handleClose = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}


interface routerRef {
  name:string,
  path:string
}
let routerArr = ref<routerRef[]>([])

const jumpPath = (child:any)=>{
  console.log(child)
  let obj = {
    name:child.name,
    path:child.url
  }
  if(JSON.stringify(routerArr.value).indexOf(JSON.stringify(obj)) === -1){
    routerArr.value.push(obj)
    store.changeRouterArr(routerArr.value)
  }
  console.log(routerArr.value)
}

const closeRouter = (index:number)=>{
  console.log('关闭',index)
  routerArr.value.splice(index,1)
  let length:number = routerArr.value.length
  if(length > 0){
    router.push(routerArr.value[length - 1].path)
    store.changeRouterArr(routerArr.value)
  }else{

  }
}

const changemsg = ref()
const changeSelf = ()=>{
  console.log(store.userInfo)
  changemsg.value.open()
  let form = {
    nicker:store.userInfo.nicker,
    avatar:store.userInfo.avatar,
    email:store.userInfo.email,
    phone:store.userInfo.phone,
  }
  changemsg.value.form = form
}
const changepwd = ref()
const changePwd = ()=>{
  changepwd.value.open()
}
const loginOut = ()=>{
  logoutApi({}).then((res:any)=>{
    console.log('退出',res)
    if(res.code == 0){
      store.$reset();
      router.push('/login')
    }
  })
  
}
const scrollbar = ref()
const jumpNav = (index:number)=>{
 scrollbar.value.setScrollLeft(index*50)
}
</script>
<style scoped lang="scss">
.home{
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: space-between;
  .menu {
    height: 100vh;
    background-color: #545c64;
    color: #fff;
    margin-right: 5px;
    h3{
      height: 70px;
      line-height: 70px;
      text-align: center;
    }
    .el-menu{
      max-height: calc(100vh - 70px);
      border-right: 0;
    }
  }
  .contentBox{
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow: hidden;
    header{
      width: 100%;
      height: 60px;
      background-color: #fff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 20px;
      box-sizing: border-box;
      .header_left{
        width: 80%;
        height: 100%;
        .icon{
          cursor: pointer;
        }
        .scrollBox{
          width: 80%;
          margin-left: 20px;
        }
        // 隐藏横向滚动条
        // :deep(.el-scrollbar__bar.is-horizontal) {
        //     height: 0 !important;
        // }
        .nav{
          display: flex;
          a{
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100px;
            height: 40px;
            padding: 0px 10px;
            box-sizing: border-box;
            color: #4E5969;
            font-size: 14px;
            position: relative;
            border-radius: 5px;
            .icon{
              position: absolute;
              top: 15%;
              right: 5%;
              display: none;
            }
          }
          a:hover{
            .icon{
              display: block;
            }
          }
          .linkActive{
            color: #FFD04B;
            background-color: #f7f8fa;
          }
        }
      }
      .headBox{
        .el-dropdown-link{
          display: flex;
          align-items: center;
        }
        .img{
          height: 50px;
          margin-right: 10px;
        }
      }
    }
    .content{
      height: calc(100vh - 60px);
      margin: 10px ;
      &>div{
        width: 100%;
        height: 100%;
        padding: 20px;
        box-sizing: border-box;
        overflow: auto;
      }
    }
  }
}

</style>