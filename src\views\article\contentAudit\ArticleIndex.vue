<template>
  <div class="index">
    <div class="content">
      <h2>{{form.title}}</h2>
      <img v-if="form.pic" :src="store.hw + form.pic" alt="">
      <div class="con" v-html="form.content"></div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref,onMounted } from 'vue'
import { artArticleInfoApi } from '@/api/api'
import { useRoute } from 'vue-router'
const route = useRoute()
import { useStore } from '@/store/Index'
const store = useStore()

let form = ref ({
  title:'',
  content:'',
  pic:''
})

onMounted(()=>{
  console.log(route.params.id)
  getArticle()
})

const getArticle = () => {
  artArticleInfoApi(Number(route.params.id)).then((res:any)=>{
    console.log('详情',res)
    if(res.code == 0){
      form.value = res.data
    }else{
      ElMessage.error(res.msg)
    }
  })
}


</script>

<style lang="scss" scoped>
.index{
  background-color: #F4F4F4;
  padding: 20px ;
  .content{
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: clamp(15px,2vh,30px) clamp(15px,2vw,50px);
    box-sizing: border-box;
    background-color: #FFF;
    h2{
      color: #365e92;
      text-align: center;
    }
    .tagBox{
      background: #F4F4F4;
      border: 1px solid #CDCDCD;
      text-align: center;
      padding: 10px 0px;
      display: flex;
      justify-content: space-evenly;
      flex-wrap: wrap;
      margin: 20px 0px;
      p{
        color: #666;
      }
    }
    .con{
      line-height: 1.5;
    }
    :deep(img){
      max-width:100% !important;
      margin:10px 0px;
    }
  }
}

</style>