<template>
  <div>
    <Layer ref="layerRef" :title="isEdit?'修改模型':'添加模型'"  @fatherShow='close'>
      <template #layer>
        <div>
          <el-form ref="formRef" :model="form" :rules="rules" label-width="auto">
            <el-form-item label="排序" prop="sort">
              <el-input  v-model="form.sort" placeholder="排序" type="number"  />
            </el-form-item>
            <el-form-item label="名称" prop="name">
              <el-input  v-model="form.name" placeholder="名称"  />
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option label="启用" :value="1" />
                <el-option label="禁用" :value="0" />
              </el-select>
            </el-form-item>
          </el-form>

          <div class="popBtn">
              <el-button type="" size="small" @click="close()">取消</el-button>
              <el-button v-if="isEdit" type="primary"  size="small" @click="save(formRef)">保存</el-button>
              <el-button v-else type="primary"  size="small" @click="submit(formRef)">确定</el-button>
            </div>
        </div>
      </template>
    </Layer>
  </div>
</template>
<script lang="ts" setup>
import Layer from '@/components/Layer.vue'
import { ref,reactive } from 'vue'
import { artModelAddApi,artModelUpdateApi } from '@/api/api'
import {type FormInstance ,type FormRules,ElMessage } from 'element-plus'

let isEdit = ref(false)

const layerRef = ref()
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' },
  ],
  status: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
})
let form = ref ({
  sort:0,
  name:'',
  status:1,
})
const emit = defineEmits(['changeData'])


const open = ()=>{
  layerRef.value.showModel = true
}
const close = ()=>{
  form.value = {
    sort:0,
    name:'',
    status:1,
  }
  console.log(form.value)
  layerRef.value.showModel = false
}


const formRef = ref<FormInstance>()
const submit = (formEl:FormInstance | undefined )=>{
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      console.log(form.value);
      artModelAddApi(form.value).then((res:any)=>{
        console.log(res);
        if(res.code == 0){
          ElMessage.success('成功')
          emit('changeData')
          close()
        }else{
          ElMessage.error(res.msg)
        }
      })
    }
  })
}

const save = (formEl:FormInstance | undefined)=>{
  console.log(form.value);
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      console.log(form);
      artModelUpdateApi(form.value).then((res:any)=>{
        console.log(res);
        if(res.code == 0){
          ElMessage.success(res.msg)
          emit('changeData')
          close()
        }else{
          ElMessage.error(res.msg)
        }
      })
    }
  })
}

defineExpose({
  isEdit,
  form,
  open,
})
</script>

<style lang="scss" scoped>


</style>