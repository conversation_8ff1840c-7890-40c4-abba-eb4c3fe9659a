<template>
  <div>
    <el-button class="mb_2" type="" icon="ArrowLeft" @click="router.go(-1)">返回</el-button>
    <div class="toolBox">
      <el-button type="primary" icon="Plus" @click="addModel">添加字段</el-button>
    </div>
    <div>
      <el-table 
        class="table"
        :data="tableData" 
        border 
        style="width: 100%"
        :header-cell-style="{ background: '#F2F2F2', textAlign: 'center' ,color:'#666'}"
        :cell-style="{ textAlign: 'center' }"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="sort" label="排序"  width="60" />
        <el-table-column prop="name" label="字段名" />
        <el-table-column prop="type" label="类型" />
        <el-table-column prop="option" label="选项" />
        <el-table-column prop="default" label="默认值" />
        <el-table-column prop="tips" label="提示" show-overflow-tooltip />
        <el-table-column  label="状态" >
          <template #default="scope">
            {{scope.row.isshow?'显示':'隐藏'}}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="200">
          <template #default="scope" >
            <el-button  type="primary" size="small" plain icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button  type="danger" size="small" plain icon="Delete" @click="handleDel(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <PageCom ref="pageCom" :total="total" @changePage='getList' />
    </div>
    <FieldPop ref="fieldPop" @changeData='getList' />
  </div>
</template>

<script lang="ts" setup>

import PageCom from '@/components/PageCom.vue'
import FieldPop from './FieldPop.vue'
import { ref,onMounted } from 'vue'
import { artModelItemListApi,artModelItemRemoveApi } from '@/api/api'
import { useRoute,useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()


onMounted(()=>{
  getList()
})


const pageCom = ref()
let total = ref(0)
let tableData = ref([])
const getList = ()=>{
  let data = {
    page:pageCom.value.currentPage,
    rows:pageCom.value.pageSize,
    // ...form
  }
  artModelItemListApi(route.params.modelId,data).then((res:any) =>{
    console.log('模型字段',res)
    if(res.code == 0){
      tableData.value = res.data
      total.value = res.data.length
    }
  })
}


interface msg{
  id:number,
  status:number,
  option:string
}

const fieldPop = ref()
const addModel = ()=>{
  fieldPop.value.open()
  fieldPop.value.isEdit = false
}
const handleEdit = (row:msg)=>{
  console.log(row)
  fieldPop.value.open()
  fieldPop.value.isEdit = true
  fieldPop.value.form = JSON.parse(JSON.stringify(row))
  if(row.option){
    let arr:Array<any> = []
    JSON.parse(row.option).forEach((item:any)=>{
      arr.push({
        value:item
      })
    })
    fieldPop.value.optionArr = arr
  }
}




const handleDel = (id:number)=>{
  ElMessageBox.confirm('是否删除该数据？','提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'error',
    }
  ).then(() => {
    artModelItemRemoveApi(route.params.modelId,id,{}).then((res:any)=>{
      if(res.code == 0){
        ElMessage.success(res.msg)
        getList()
      }else{
        ElMessage.error(res.msg)
      }
    })
    })
  .catch(() => {
    ElMessage.info('已取消')
  })
}

</script>