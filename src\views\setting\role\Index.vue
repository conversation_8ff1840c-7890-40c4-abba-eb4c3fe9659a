<template>
  <div>
    <div class="searchBox">
      <el-form ref="searchRef" :model="form" class="form">
        <el-form-item prop="name">
          <el-input prefix-icon="Search" v-model="form.name" placeholder="请输入角色名" />
        </el-form-item>
        <el-form-item prop="status">
          <el-select v-model="form.status" placeholder="请选择状态">
            <template #prefix>
              <el-icon ><Search /></el-icon>
            </template>
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getRole">搜索</el-button>
          <span class="clear" @click="resetForm(searchRef)">清除选项</span>
        </el-form-item>
      </el-form>
    </div>
    <div class="toolBox">
      <el-button type="primary" icon="Plus" @click="addRole">添加角色</el-button>
    </div>
    <div>
      <el-table 
        class="table"
        :data="tableData" 
        border 
        style="width: 100%"
        :header-cell-style="{ background: '#F2F2F2', textAlign: 'center' ,color:'#666'}"
        :cell-style="{ textAlign: 'center' }"
        tooltip-effect="dark"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="sort" label="排序" width="60" />
        <el-table-column prop="name" label="名称" />
        <el-table-column prop="memo" label="备注" show-overflow-tooltip />
        <el-table-column  label="状态" >
          <template #default="scope">
            {{scope.row.status?'启用':'禁用'}}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="300">
          <template #default="scope" >
            <el-button v-btn type="primary" size="small" plain icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button v-btn type="danger" size="small" plain icon="Delete" @click="handleDel(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <PageCom ref="pageCom" :total="total" @changePage='getRole' />
    </div>
    <RolePop ref="rolePop" @changeData="getRole" />
  </div>
</template>
<script lang="ts" setup>
import { ref,reactive,onMounted } from 'vue'
import { roleApi,roleRemoveApi,roleRulesApi } from '@/api/api'
import PageCom from '@/components/PageCom.vue'
import RolePop from './RolePop.vue'
import {  type FormInstance } from 'element-plus'


interface RuleForm {
  name: string
  status ?: number
}
const searchRef = ref<FormInstance>()
const form = reactive<RuleForm>({
  name:'',
})

onMounted(()=>{
  getRole()
  getMenu()
})

const pageCom = ref()
let tableData = ref([])
let total = ref(0)
const getRole = ()=>{
  let data = {
    page:pageCom.value.currentPage,
    rows:pageCom.value.pageSize,
    ...form
  }
  roleApi(data).then((res:any) =>{
    console.log('角色',res)
    if(res.code == 0){
      tableData.value = res.data
      total.value = res.data.length
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  getRole()
}

let menuList = ref([])
const getMenu = ()=>{
  roleRulesApi({}).then((res:any) =>{
    console.log('菜单',res)
    if(res.code == 0){
      menuList.value = res.data
    }
  })
}


interface msg{
  id:number,
  status:boolean
}

const rolePop = ref()
const addRole = ()=>{
  rolePop.value.open()
  rolePop.value.isEdit = false
  rolePop.value.menuList = menuList.value
}
const handleEdit = (row:msg)=>{
  rolePop.value.open()
  rolePop.value.isEdit = true
  rolePop.value.menuList = menuList.value
  console.log(menuList.value)
  rolePop.value.getDetail(row.id)
}


const handleDel = (id:number)=>{
  ElMessageBox.confirm('是否删除该数据？','提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'error',
    }
  ).then(() => {
    roleRemoveApi(id,{}).then((res:any)=>{
      if(res.code == 0){
        ElMessage.success('成功')
        getRole()
      }else{
        ElMessage.error(res.msg)
      }
    })
    })
  .catch(() => {
    ElMessage.info('已取消')
  })
}
</script>