<template>
  <div>
    <div class="searchBox">
      <el-form ref="searchRef" :model="form" class="form">
        <el-form-item prop="name">
          <el-input prefix-icon="Search" v-model="form.name" placeholder="请输入管理员昵称" />
        </el-form-item>
        <el-form-item prop="roleId">
          <el-select v-model="form.roleId" placeholder="请选择角色" clearable >
            <template #prefix>
              <el-icon ><Search /></el-icon>
            </template>
            <el-option v-for="item in roleList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item prop="status">
          <el-tree-select
            ref="treeRef"
            v-model="form.departmentId"
            :data="departmentList"
            :render-after-expand="false"
            show-checkbox
            placeholder="请选择部门"
            clearable
          />
        </el-form-item>
        <el-form-item prop="status">
          <el-select v-model="form.status" placeholder="请选择状态" clearable>
            <template #prefix>
              <el-icon ><Search /></el-icon>
            </template>
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="9" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getAdmin">搜索</el-button>
          <span class="clear" @click="resetForm(searchRef)">清除选项</span>
        </el-form-item>
      </el-form>
    </div>
    <div class="toolBox">
      <el-button type="primary" icon="Plus" @click="addAdmin">添加管理员</el-button>
    </div>
    <div>
      <el-table 
        class="table"
        :data="tableData" 
        border 
        style="width: 100%"
        :header-cell-style="{ background: '#F2F2F2', textAlign: 'center' ,color:'#666'}"
        :cell-style="{ textAlign: 'center' }"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="account" label="账号" />
        <el-table-column prop="name" label="昵称" show-overflow-tooltip />
        <el-table-column prop="Role.name" label="角色" show-overflow-tooltip />
        <el-table-column  label="超级管理" >
          <template #default="scope">
            {{scope.row.isRoot == 1 ? '是' : '否'}}
          </template>
        </el-table-column>
        <el-table-column  label="状态" >
          <template #default="scope">
            {{scope.row.status == 1?'启用':scope.row.status == 9?'禁用':''}}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="400">
          <template #default="scope" >
            <div v-if="scope.row.id != 1">
              <el-button v-btn type="info" size="small" plain icon="refresh" @click="handleActive(scope.row)">状态改变</el-button>
              <el-button v-btn type="warning" size="small" plain icon="refresh" @click="handleReset(scope.row)">重置密码</el-button>
              <el-button v-btn type="primary" size="small" plain icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button v-btn type="danger" size="small" plain icon="Delete" @click="handleDel(scope.row.id)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <PageCom ref="pageCom" :total="total" @changePage='getAdmin' />
    </div>
    <AdminPop ref="adminPop" @changeData="getAdmin" />
  </div>
</template>
<script lang="ts" setup>
import PageCom from '@/components/PageCom.vue'
import { ref,reactive,onMounted } from 'vue'
import { adminApi,adminRemoveApi,departmentApi,adminActiveApi,roleApi,adminResetApi } from '@/api/api'
import AdminPop from './AdminPop.vue'
import {  type FormInstance } from 'element-plus'

let total = ref(0)
interface RuleForm {
  name: string
  departmentId ?: number
  roleId ?: number
  status ?: number
}
const searchRef = ref<FormInstance>()
const form = reactive<RuleForm>({
  name:'',
})

onMounted(()=>{
  getAdmin()
  getDepartment()
  getRole()
})

const pageCom = ref()
let tableData = ref([])
const getAdmin = ()=>{
  let data = {
    page:pageCom.value.currentPage,
    rows:pageCom.value.pageSize,
    ...form
  }
  adminApi(data).then((res:any) =>{
    console.log('管理员',res)
    if(res.code == 0){
      tableData.value = res.data
      total.value = res.data.length
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  getAdmin()
}

interface roleType {
  id:number
  name:string
}

let departmentList:Ref<roleType[]> = ref([])
const getDepartment = ()=>{
  departmentApi({}).then((res:any) =>{
    console.log('部门',res)
    if(res.code == 0){
      res.data.forEach((item:any) => {
        item.label = item.name
        item.value = item.id
        if(item.children && item.children.length > 0){
          item.children.forEach((child:any)=>{
            child.label = child.name
            child.value = child.id
            if(child.children && child.children.length > 0){
              child.children.forEach((grandson:any)=>{
                grandson.label = grandson.name
                grandson.value = grandson.id
              })
            }
            
          })
        }
      })
      departmentList.value = res.data
    }
  })
}

let roleList:Ref<roleType[]> = ref([])
const getRole = ()=>{
  roleApi({}).then((res:any) =>{
    console.log('角色',res)
    if(res.code == 0){
      roleList.value = res.data
    }
  })
}

interface msg{
  id:number,
  status:boolean
}

const adminPop = ref()
const addAdmin = ()=>{
  adminPop.value.open()
  adminPop.value.isEdit = false
  adminPop.value.departmentList = departmentList.value
  adminPop.value.roleList = roleList.value
}
const handleEdit = (row:msg)=>{
  adminPop.value.open()
  adminPop.value.isEdit = true
  adminPop.value.departmentList = departmentList.value
  adminPop.value.roleList = roleList.value
  adminPop.value.getDetail(row.id)
}


const handleDel = (id:number)=>{
  ElMessageBox.confirm('是否删除该数据？','提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'error',
    }
  ).then(() => {
    adminRemoveApi(id,{}).then((res:any)=>{
      if(res.code == 0){
        ElMessage.success('成功')
        getAdmin()
      }else{
        ElMessage.error(res.msg)
      }
    })
    })
  .catch(() => {
    ElMessage.info('已取消')
  })
}
const handleActive = (row:msg)=>{
  let status = Number(row.status) == 1 ? 'ban' : 'start'
  ElMessageBox.confirm('是否改变该管理员状态？','提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    adminActiveApi(row.id,status,{}).then((res:any)=>{
      if(res.code == 0){
        ElMessage.success('成功')
        getAdmin()
      }else{
        ElMessage.error(res.msg)
      }
    })
    })
  .catch(() => {
    ElMessage.info('已取消')
  })
}
const handleReset = (row:msg)=>{
  ElMessageBox.confirm('是否重置密码？','提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    adminResetApi(row.id,{}).then((res:any)=>{
      if(res.code == 0){
        ElMessage.success(res.msg)
      }else{
        ElMessage.error(res.msg)
      }
    })
    })
  .catch(() => {
    ElMessage.info('已取消')
  })
}
</script>