<template>
  <div class="page">
    <el-pagination
      v-model:currentPage="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[100, 200, 300, 400]"
      background
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      small 
      :pager-count="5"
    />
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
 let currentPage = ref(1)
 let pageSize = ref(50)
 const emit = defineEmits(['changePage'])

defineProps({
    total:{
      type:Number,
      default:0
    }
})

defineExpose({
  currentPage,
  pageSize
})

// 方法
const handleSizeChange = (val:number)=>{
  console.log(`${val} items per page`)
  emit('changePage',{
    'current':currentPage.value,
    'pageSize':val
  })
}
const handleCurrentChange = (val:number)=>{
  console.log(`current page: ${val}`)
  emit('changePage',{
    'current':val,
    'pageSize':pageSize.value
  })
}

</script>
<style lang="scss" scoped>
.page{
  display: flex;
  align-items:center ;
  // justify-content: center;
  margin: 20px 0;
  :deep(.el-pagination__editor.el-input){
    width: 50px;
  }
}
</style>