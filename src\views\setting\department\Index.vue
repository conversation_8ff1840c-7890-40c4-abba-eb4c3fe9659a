<template>
  <div>
    <div class="toolBox">
      <el-button type="primary" icon="Plus" @click="addDepartment(0)">添加部门</el-button>
    </div>
    <div>
      <el-table 
        class="table"
        :data="tableData" 
        border 
        style="width: 100%"
        :header-cell-style="{ background: '#F2F2F2', textAlign: 'center' ,color:'#666'}"
        :cell-style="{ textAlign: 'center' }"
        row-key="id"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="sort" label="排序" width="60" />
        <el-table-column prop="name" label="名称" />
        <el-table-column prop="memo" label="备注" show-overflow-tooltip />
        <el-table-column fixed="right" label="操作" width="300">
          <template #default="scope" >
            <el-button v-btn type="primary" size="small" plain icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button v-btn  type="danger" size="small" plain icon="Delete" @click="handleDel(scope.row.id)">删除</el-button>
            <el-button v-btn type="warning" size="small" plain icon="Plus"
            @click="addDepartment(scope.row.id)">添加子部门</el-button>
          </template>
        </el-table-column>
      </el-table>
      <PageCom ref="pageCom" :total="total" @changePage='getDepartment' />
    </div>
    <DepartmentPop ref="adepartmentPop" @changeData="getDepartment" />
  </div>
</template>
<script lang="ts" setup>
import PageCom from '@/components/PageCom.vue'
import { ref,onMounted } from 'vue'
import { departmentApi,departmentRemoveApi } from '@/api/api'
import DepartmentPop from './DepartmentPop.vue'



onMounted(()=>{
  getDepartment()
})

const pageCom = ref()
let tableData = ref([])
let total = ref(0)
const getDepartment = ()=>{
  let data = {
    page:pageCom.value.currentPage,
    rows:pageCom.value.pageSize,
  }
  departmentApi(data).then((res:any) =>{
    console.log('部门',res)
    if(res.code == 0){
      tableData.value = res.data
      total.value = res.data.total
    }
  })
}

interface msg{
  id:number,
}

const adepartmentPop = ref()
const addDepartment = (pid:number)=>{
  adepartmentPop.value.open()
  adepartmentPop.value.isEdit = false
  adepartmentPop.value.form.pid = pid
}
const handleEdit = (row:msg)=>{
  adepartmentPop.value.open()
  adepartmentPop.value.isEdit = true
  adepartmentPop.value.form = JSON.parse(JSON.stringify(row))
}


const handleDel = (id:number)=>{
  ElMessageBox.confirm('是否删除该数据？','提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'error',
    }
  ).then(() => {
    departmentRemoveApi(id,{}).then((res:any)=>{
      if(res.code == 0){
        ElMessage.success('成功')
        getDepartment()
      }else{
        ElMessage.error(res.msg)
      }
    })
    })
  .catch(() => {
    ElMessage.info('已取消')
  })
}

</script>