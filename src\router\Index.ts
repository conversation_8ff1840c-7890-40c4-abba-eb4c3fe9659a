import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    redirect:'/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: ()=> import('@/views/Login.vue')
  },
  {
    path: '/home',
    component: ()=> import('@/views/Home.vue'),
    children:[
      {
        path: '/setting',
        component: ()=> import('@/views/setting/Index.vue'),
        meta:{
          title:'设置'
        }
      },
      {
        path: '/setting/role',
        component: ()=> import('@/views/setting/role/Index.vue'),
        meta:{
          title:'角色管理'
        }
      },
      {
        path: '/setting/admin',
        component: ()=> import('@/views/setting/admin/Index.vue'),
        meta:{
          title:'管理员管理'
        }
      },
      {
        path: '/setting/department',
        component: ()=> import('@/views/setting/department/Index.vue'),
        meta:{
          title:'部门管理'
        }
      },
      {
        path: '/syslog',
        component: ()=> import('@/views/syslog/Index.vue'),
        meta:{
          title:'日志管理'
        }
      },
      {
        path: '/article/cat',
        component: ()=> import('@/views/article/cat/Index.vue'),
        meta:{
          title:'文章栏目'
        }
      },
      {
        path: '/article/model',
        component: ()=> import('@/views/article/model/Index.vue'),
        meta:{
          title:'文章模型'
        }
      },
      {
        path: '/article/model/:modelId/view',
        component: ()=> import('@/views/article/model/FieldView.vue'),
        name:'modelView',
        meta:{
          title:'模型预览'
        }
      },
      {
        path: '/article/model/:modelId/field',
        component: ()=> import('@/views/article/model/Field.vue'),
        name:'modelField',
        meta:{
          title:'模型字段'
        }
      },
      {
        path: '/article/content',
        component: ()=> import('@/views/article/content/Index.vue'),
        meta:{
          title:'文章内容'
        },
        children:[
          {
            path: '/article/contentcon/:catId/:catName',
            component: ()=> import('@/views/article/content/ContentList.vue'),
            name:'contentList',
            meta:{
              title:'文章列表'
            }
          },
          {
            path: '/article/contentIndex/:catId/:catName',
            component: ()=> import('@/views/article/content/ContentIndex.vue'),
            name:'contentIndex',
            meta:{
              title:''
            }
          },
        ]
      },
      {
        path: '/article/contentAudit',
        component: ()=> import('@/views/article/contentAudit/Index.vue'),
        meta:{
          title:'文章内容'
        },
        children:[
          {
            path: '/article/contentAuditcon/:catId/:catName',
            component: ()=> import('@/views/article/contentAudit/ContentList.vue'),
            name:'contentAuditList',
            meta:{
              title:'文章列表'
            }
          },
          {
            path: '/article/contentAuditIndex/:catId/:catName',
            component: ()=> import('@/views/article/contentAudit/ContentIndex.vue'),
            name:'contentAuditIndex',
            meta:{
              title:''
            }
          },
        ]
      },
      // 网站配置
      {
        path: '/webConfig/friendLink',
        component: ()=> import('@/views/webConfig/friendLink/Index.vue'),
        meta:{
          title:'友链管理'
        }
      },
      {
        path: '/webConfig/ad',
        component: ()=> import('@/views/webConfig/ad/Index.vue'),
        meta:{
          title:'广告管理'
        }
      },
      {
        path: '/webConfig/common',
        component: ()=> import('@/views/webConfig/common/Index.vue'),
        meta:{
          title:'公共配置'
        }
      },
      {
        path: '/webConfig/models',
        component: ()=> import('@/views/webConfig/models/Index.vue'),
        meta:{
          title:'模块配置'
        }
      },
    ]
  },
  // 文章详情预览
  {
    path: '/article/detail/:id',
    name: 'articleDetail',
    component: ()=> import('@/views/article/contentAudit/ArticleDetail.vue')
  },
  // 单页预览
  {
    path: '/article/index/:id',
    name: 'articleIndex',
    component: ()=> import('@/views/article/contentAudit/ArticleIndex.vue')
  }
] 
 
const router = createRouter({
  linkActiveClass: 'linkActive',
  history: createWebHistory('backend'),
  routes
})
 
export default router