# 链接管理重命名功能测试

## 功能概述

将系统中所有"广告"、"入口"相关的术语统一修改为"链接管理"，并修复了删除确认弹窗的样式问题。

## 主要变更

### 1. 术语统一修改

**变更范围：**
- 界面显示文字
- 表单标签
- 提示信息
- 确认对话框
- 成功/错误消息

**变更对照表：**
| 原术语 | 新术语 |
|--------|--------|
| 广告位置 | 链接位置 |
| 广告内容 | 链接内容 |
| 广告名称 | 链接名称 |
| 广告链接 | 链接地址 |
| 广告图片 | 链接图片 |
| 广告描述 | 链接描述 |
| 管理广告内容 | 管理链接内容 |
| 添加广告 | 添加链接 |
| 编辑广告 | 编辑链接 |
| 删除广告 | 删除链接 |

### 2. 删除确认弹窗样式修复

**问题：**
- 删除确认弹窗没有居中显示
- 缺少背景遮罩
- 层级问题导致样式错乱

**修复方案：**
```vue
<!-- 主弹窗增加层级控制 -->
<el-dialog 
  :z-index="3000"
  append-to-body
>

<!-- 确认弹窗增加样式配置 -->
await ElMessageBox.confirm(message, title, {
  center: true,
  customClass: 'delete-confirm-dialog'
})
```

**CSS修复：**
```css
:deep(.delete-confirm-dialog) {
  z-index: 4000 !important;
}

:deep(.el-message-box) {
  z-index: 4000 !important;
}

:deep(.el-overlay) {
  z-index: 3999 !important;
}
```

## 测试用例

### 1. 界面文字显示测试

**测试步骤：**
1. 访问"网站配置 > 模块配置"
2. 检查左侧可用模块区域的标题
3. 点击"添加链接位置"
4. 检查布局预览中的显示文字

**预期结果：**
- 左侧显示"链接位置"而不是"广告位置"
- 按钮文字为"添加链接位置"
- 布局预览中显示"链接位置：xxx"
- 数量显示为"x个链接"

### 2. 配置弹窗文字测试

**测试步骤：**
1. 添加一个链接位置
2. 点击"配置"按钮
3. 检查配置弹窗中的所有标签和文字

**预期结果：**
- 表单标签显示"链接内容"
- 按钮文字为"管理链接内容"
- 提示文字包含"链接"而不是"广告"

### 3. 链接管理弹窗文字测试

**测试步骤：**
1. 点击"管理链接内容"按钮
2. 检查弹窗标题和内容
3. 点击"添加链接"按钮
4. 检查编辑表单的标签

**预期结果：**
- 弹窗标题为"管理链接位置：xxx"
- 工具栏按钮为"添加链接"
- 编辑弹窗标题为"添加链接"/"编辑链接"
- 表单标签为"链接名称"、"链接地址"等

### 4. 删除确认弹窗样式测试

**测试步骤：**
1. 在链接管理弹窗中添加一个链接
2. 点击该链接的"删除"按钮
3. 检查确认弹窗的显示效果

**预期结果：**
- 确认弹窗正确居中显示
- 有半透明背景遮罩
- 弹窗层级正确，不被其他元素遮挡
- 文字内容为"确定要删除链接"xxx"吗？"

### 5. 批量删除确认弹窗测试

**测试步骤：**
1. 选择多个链接
2. 点击"批量删除"按钮
3. 检查确认弹窗的显示和文字

**预期结果：**
- 弹窗样式正确
- 文字为"确定要删除选中的 x 个链接吗？"
- 删除后提示"批量删除成功，共删除 x 个链接"

### 6. 位置删除确认弹窗测试

**测试步骤：**
1. 在主配置页面删除一个有链接的位置
2. 检查确认弹窗的文字和样式

**预期结果：**
- 弹窗样式正确
- 文字为"确定要删除链接位置"xxx"吗？"
- 警告文字为"该位置下有 x 个链接，删除位置时会同步删除所有链接内容！"

### 7. 成功消息测试

**测试步骤：**
1. 执行各种操作（添加、编辑、删除）
2. 检查成功提示消息的文字

**预期结果：**
- 添加成功："已添加链接位置：index_x"
- 删除成功："已删除位置 index_x 下的 x 个链接"
- 位置删除："链接位置已删除"

## 代码变更统计

### 1. 文件修改列表

- `src/views/webConfig/models/Index.vue`
- `src/views/webConfig/models/EntranceManageDialog.vue`
- `src/views/webConfig/models/EntranceEditDialog.vue`

### 2. 主要变更类型

**界面文字变更：**
- 模板中的显示文字
- 表单标签
- 按钮文字
- 提示信息

**功能逻辑变更：**
- 删除确认弹窗样式修复
- 层级控制优化
- 错误处理改进

**样式修复：**
- z-index层级控制
- 弹窗居中显示
- 背景遮罩修复

## 兼容性说明

### 1. 数据兼容性

- 数据库结构无变更
- API接口无变更
- 配置格式无变更
- 仅界面显示文字变更

### 2. 功能兼容性

- 所有原有功能保持不变
- 操作流程无变化
- 数据处理逻辑无变更

### 3. 样式兼容性

- 修复了弹窗样式问题
- 不影响其他组件样式
- 向后兼容现有主题

## 注意事项

### 1. 术语一致性

- 确保所有相关文档也更新术语
- 用户手册需要同步更新
- API文档中的描述需要检查

### 2. 用户适应性

- 用户可能需要时间适应新术语
- 建议在更新说明中提及术语变更
- 考虑提供术语对照表

### 3. 测试覆盖

- 确保所有界面文字都已更新
- 检查是否有遗漏的术语
- 验证多语言环境下的显示

## 后续优化建议

### 1. 国际化支持

- 将硬编码文字提取为语言包
- 支持多语言切换
- 统一术语管理

### 2. 样式优化

- 统一弹窗样式规范
- 优化层级管理机制
- 改进用户交互体验

### 3. 功能增强

- 添加链接有效性检查
- 支持链接分组管理
- 增加链接使用统计
