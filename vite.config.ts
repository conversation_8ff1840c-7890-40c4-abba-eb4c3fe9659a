import { defineConfig,loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
//引入elementplus
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import path from 'path'

const pathSrc = path.resolve(__dirname, 'src')

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());

  return{
    base:"/backend/",
    plugins: [
      vue(),
      AutoImport({
        resolvers: [ElementPlusResolver()],
      }),
      Components({
        resolvers: [ElementPlusResolver()],
      }),
    ],
    build: {
      // 添加以下配置去除打包后的代码中的console
      // minify: 'terser',
      // terserOptions: {
      //   compress: {
      //     //生产环境时移除console.log(), console.info, console.warn, console.error, 或者直接赋值true移除一切console.*的代码
      //     drop_console: true,
      //     drop_debugger: true,
      //   },
      // },
      // 打包文件过大
      chunkSizeWarningLimit: 1600,
      rollupOptions: {
        output: {
          // 分包
          manualChunks(id) {
            if (id.includes('node_modules')) {
              return id.toString().split('node_modules/')[1].split('/')[0].toString()
            }
          }
        }
      }
    },
    // 别名
    resolve:{
      alias:{
        '@':path.resolve(__dirname,'./src'),
      }
    },
    // 代理
    server: {
      // host:'**************',
      // port:'5173',
      // proxy: {
      //   [env.VITE_APP_BASE_API]: {
      //     target: env.VITE_APP_REQUEST_URL, //需要代理的域名，目标域名
      //     changeOrigin: true, //需要代理跨域
      //     // rewrite: (path) => path.replace(/^\/api/, ''), //路径重写，把'/api'替换为''
      //   },
      // },
    }
  }
})
