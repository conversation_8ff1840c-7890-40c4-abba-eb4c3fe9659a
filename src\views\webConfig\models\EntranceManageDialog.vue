<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`管理链接位置：${positionName}`"
    width="80%"
    :before-close="handleClose"
    append-to-body
    destroy-on-close
  >
    <div class="entrance-manage-content">
      <!-- 搜索区域 -->
      <div class="search-box">
        <el-form ref="searchRef" :model="searchForm" inline>
          <el-form-item label="标题">
            <el-input 
              v-model="searchForm.name" 
              placeholder="请输入标题" 
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px">
              <el-option label="启用" value="1" />
              <el-option label="禁用" value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="getList">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 工具栏 -->
      <div class="toolbar">
        <el-button type="primary" icon="Plus" @click="addEntrance">添加链接</el-button>
        <el-button type="danger" icon="Delete" @click="batchDelete" :disabled="selectedIds.length === 0">
          批量删除
        </el-button>
      </div>

      <!-- 表格 -->
      <el-table 
        ref="tableRef"
        :data="tableData" 
        border 
        style="width: 100%"
        @selection-change="handleSelectionChange"
        :header-cell-style="{ background: '#F2F2F2', textAlign: 'center', color: '#666' }"
        :cell-style="{ textAlign: 'center' }"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="sort" label="排序" width="80">
          <template #default="scope">
            <el-input-number 
              v-model="scope.row.sort" 
              :min="0" 
              size="small"
              @change="updateSort(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="name" label="名称" show-overflow-tooltip />
        <el-table-column prop="link" label="链接" show-overflow-tooltip />
        <el-table-column label="图片" width="120">
          <template #default="scope">
            <el-image 
              v-if="scope.row.path" 
              style="width: 80px; height: 50px;" 
              :src="store.hw + scope.row.path"
              fit="cover"
            />
            <span v-else class="no-image">无图片</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80">
          <template #default="scope">
            <el-switch 
              v-model="scope.row.status" 
              :active-value="1" 
              :inactive-value="0"
              @change="updateStatus(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button size="small" @click="editEntrance(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteEntrance(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 广告编辑弹窗 -->
    <EntranceEditDialog 
      ref="editDialogRef"
      :position-key="currentPosition"
      @refresh="getList"
    />
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { bannersApi, bannersRemoveApi } from '@/api/api'
import { useStore } from '@/store/Index'
import EntranceEditDialog from './EntranceEditDialog.vue'

const store = useStore()
const emit = defineEmits(['refresh'])

// 弹窗控制
const dialogVisible = ref(false)
const currentPosition = ref('')
const positionName = ref('')

// 搜索表单
const searchForm = reactive({
  name: '',
  status: ''
})

// 表格数据
const tableData = ref([])
const selectedIds = ref([])

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 编辑弹窗引用
const editDialogRef = ref()

// 打开弹窗
const open = (positionKey: string, name: string) => {
  currentPosition.value = positionKey
  positionName.value = name
  dialogVisible.value = true
  resetSearch()
  getList()
}

// 获取列表数据
const getList = async () => {
  try {
    const params = {
      page: pagination.currentPage,
      rows: pagination.pageSize,
      pos: currentPosition.value,
      ...searchForm
    }
    
    const res: any = await bannersApi(params)
    if (res.code === 0) {
      tableData.value = res.data
      pagination.total = res.total || res.data.length
    }
  } catch (error) {
    ElMessage.error('获取数据失败')
  }
}

// 重置搜索
const resetSearch = () => {
  searchForm.name = ''
  searchForm.status = ''
  pagination.currentPage = 1
  getList()
}

// 添加广告
const addEntrance = () => {
  editDialogRef.value?.open('add', null, currentPosition.value)
}

// 编辑广告
const editEntrance = (row: any) => {
  editDialogRef.value?.open('edit', row, currentPosition.value)
}

// 删除链接
const deleteEntrance = async (row: any) => {
  if (!row || !row.id) {
    ElMessage.error('无效的链接数据')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除链接"${row.name || '未命名'}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
        appendTo: document.body
      }
    )

    const res: any = await bannersRemoveApi(row.id)
    if (res.code === 0) {
      ElMessage.success('删除成功')

      // 如果当前页没有数据了，回到上一页
      if (tableData.value.length === 1 && pagination.currentPage > 1) {
        pagination.currentPage--
      }

      await getList()
      emit('refresh') // 通知父组件刷新
    } else {
      ElMessage.error(res.msg || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除链接失败:', error)
      ElMessage.error('删除操作失败，请重试')
    }
  }
}

// 批量删除
const batchDelete = async () => {
  if (selectedIds.value.length === 0) {
    ElMessage.warning('请选择要删除的项目')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedIds.value.length} 个链接吗？\n删除后无法恢复！`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        appendTo: document.body
      }
    )

    let successCount = 0
    let failCount = 0

    // 逐个删除并统计结果
    for (const id of selectedIds.value) {
      try {
        const res: any = await bannersRemoveApi(id)
        if (res.code === 0) {
          successCount++
        } else {
          failCount++
        }
      } catch (error) {
        failCount++
      }
    }

    // 显示删除结果
    if (failCount === 0) {
      ElMessage.success(`批量删除成功，共删除 ${successCount} 个链接`)
    } else {
      ElMessage.warning(`删除完成，成功 ${successCount} 个，失败 ${failCount} 个`)
    }

    selectedIds.value = []

    // 如果当前页没有数据了，回到第一页
    if (tableData.value.length <= selectedIds.value.length && pagination.currentPage > 1) {
      pagination.currentPage = 1
    }

    await getList()
    emit('refresh') // 通知父组件刷新
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除操作失败，请重试')
    }
  }
}

// 更新状态
const updateStatus = async (row: any) => {
  try {
    // 这里需要根据实际API调整状态更新逻辑
    ElMessage.success('状态更新成功')
  } catch (error) {
    ElMessage.error('状态更新失败')
    // 恢复原状态
    row.status = row.status === 1 ? 0 : 1
  }
}

// 更新排序
const updateSort = async (row: any) => {
  try {
    // 这里需要根据实际API调整排序更新逻辑
    ElMessage.success('排序更新成功')
  } catch (error) {
    ElMessage.error('排序更新失败')
  }
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedIds.value = selection.map(item => item.id)
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  getList()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  getList()
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  currentPosition.value = ''
  positionName.value = ''
}

// 暴露方法
defineExpose({
  open
})
</script>

<style scoped>
.entrance-manage-content {
  padding: 0;
}

.search-box {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.toolbar {
  margin-bottom: 20px;
  display: flex;
  gap: 12px;
  align-items: center;
}

.toolbar .el-button {
  border-radius: 6px;
  padding: 10px 20px;
}

.no-image {
  color: #999;
  font-size: 12px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  padding: 16px 0;
  background: #fafbfc;
  border-radius: 8px;
}

/* Element Plus 自动管理弹窗层级，移除自定义 z-index */

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background: #f8f9fa !important;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f2f5;
}

:deep(.el-table tr:hover td) {
  background: #f8f9fa !important;
}
</style>
