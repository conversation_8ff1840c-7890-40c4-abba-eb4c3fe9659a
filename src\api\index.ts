import axios from 'axios';
import router from '@/router/Index';

let base_url:string = ''
if (import.meta.env.MODE == 'development') {
    base_url = 'https://cms.test.jijiaox.com/';
} else {
    base_url = window.location.protocol+'//' + window.location.host+'/';
}
console.log(base_url)

// 创建axios实例
const service = axios.create({
    baseURL: base_url,// 服务接口请求
    withCredentials : true,//跨域请求发送cookie
    // timeout: 15000,// 超时设置
    // headers:{'Content-Type':'application/json;charset=utf-8'}
})

let loading:any;
//正在请求的数量
let requestCount:number = 0
//展示loading
const showLoading = () => {
    if (requestCount === 0) {
        loading = ElLoading.service({
            text: "拼命加载中，请稍后...",
            background: 'rgba(0, 0, 0, 0.7)',
        })
    }
    requestCount++;
}
//隐藏loading
const hideLoading = () => {
    requestCount--
    if (requestCount == 0) {
        loading.close()
    }
}

//请求拦截器
service.interceptors.request.use(
    config => {
        showLoading()
        return config;
    },
    error => {
        console.log('配置失败')
        return Promise.reject(error);
    }
)
// 返回拦截
service.interceptors.response.use(
    response => {
        hideLoading()
        // console.log(response,'*****************************')
        if (response.status === 200) {
            if (response.data.code === 403) {
                ElMessage.error({
                    showClose: true,
                    message: response.data.msg,
                })
                ElMessage.error({
                    showClose: true,
                    message: '请重新登录',
                })
                setTimeout(() => {
                    router.replace({
                        path: '/',
                    });
                    sessionStorage.clear();
                }, 1000);
                
            }else if(response.data.code === 401){
                ElMessage.error({
                    showClose: true,
                    message: response.data.msg,
                })
                router.replace({name:'changepwd'});
            }else if(response.data.code === 503){
                ElMessage.error({
                    showClose: true,
                    message: '服务器维护中，请等待',
                })
            }
            return Promise.resolve(response);
        } else {
            return Promise.reject(response);
        }
    },
    error => {
        console.log(error);
        if(error.response.status === 404){
            console.log(404)
            ElMessage.error({
                showClose: true,
                message: error.response.status+'接口错误,请刷新',
            })
        }else{
            ElMessage.error({
                showClose: true,
                message: '前方拥堵，请稍后刷新...',
            })
        }
        return Promise.reject(error) //接口500抛出异常（不走页面逻辑）
    }
);

export default service;