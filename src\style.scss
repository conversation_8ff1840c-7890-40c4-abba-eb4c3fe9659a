:root {
  font-family: Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

a {
  font-weight: 500;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: #f2f3f5;
}

p,h1,h2,h3,h4,h5,h6,ul,li{
  margin: 0;
  padding: 0;
}

li{
  list-style: none;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

.fff{
  background-color: #fff;
}
.flex_center{
  display: flex;
  align-items: center;
}
.flex{
  display: flex;
  justify-content: space-between;
}

.ml_1{
  margin-left: 10px;
}
.mb_1{
  margin-bottom: 10px;
}
.mb_2{
  margin-bottom: 20px;
}
.mlr_1{
  margin:0px 10px;
}



/* 单行文本 */
.elli{  
  overflow: hidden;   
  text-overflow:ellipsis;   
  white-space: nowrap;
}
/* 多行文本 */
.ellis{
  display: -webkit-box;   
  -webkit-box-orient: vertical;   
  -webkit-line-clamp: 2;   
  overflow: hidden;
}

/* loading */
.el-loading-spinner{
  color: #409eff;
}
.el-loading-spinner .el-loading-text{
  font-weight: 600;
  font-size: 18px !important;
}

.toolBox{
  margin-bottom: 20px;
}

// 弹窗按钮
.popBtn{
  display: flex;
  justify-content: space-evenly;
  margin-top: 25px;
  .el-button+.el-button{
    margin: 0 !important;
  }
}

// 搜索
.searchBox{
  margin-bottom: 20px;
  .form{
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .el-form-item{
      margin-right: 10px;
      margin-bottom: 10px;
    }
    .clear{
      color: #409eff;
      cursor: pointer;
      margin-left: 10px;
    }
  }
}

.el-select{
  width: 100%;
}


/*控制整个滚动条*/
::-webkit-scrollbar {
	background-color: transparent;
	width: 6px;
	height: 6px;
	background-clip: padding-box;
}

/*滚动条两端方向按钮*/
::-webkit-scrollbar-button {
	background-color: transparent;
	width: 0;
	height: 0;
}

/*滚动条中间滑动部分*/
::-webkit-scrollbar-thumb {
	background-color: rgba(170, 170, 170, 0.548);
	border-radius: 5px;
}

/*滚动条右下角区域*/
::-webkit-scrollbar-corner {
	background-color: transparent;
	width: 0;
	height: 0;
}