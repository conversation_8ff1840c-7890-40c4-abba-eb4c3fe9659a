<template>
  <div>
    <Layer ref="layerRef" :title="isEdit?'修改管理员':'添加管理员'"  @fatherShow='close(formRef)'>
      <template #layer>
        <div>
          <el-form ref="formRef" :model="form" :rules="rules" label-width="auto">
            <div class="flex">
              <div>
                <el-form-item label="账号" prop="account">
                  <el-input  v-model="form.account" placeholder="账号"  />
                </el-form-item>
                <el-form-item label="昵称" prop="name">
                  <el-input  v-model="form.name" placeholder="昵称"  />
                </el-form-item>
                <el-form-item label="部门" prop="departmentIdList">
                  <el-tree-select
                    ref="treeRef"
                    v-model="form.departmentIdList"
                    :data="departmentList"
                    :render-after-expand="false"
                    show-checkbox
                    multiple
                  />
                </el-form-item>
              </div>
              <div class="ml_1">
                <el-form-item label="密码" prop="password">
                  <el-input v-if="isEdit" v-model="form.password" placeholder="不修改请留空"  />
                  <el-input v-else v-model="form.password" placeholder="密码必须包含至少一个大写字母、一个小写字母、一个数字并且长度必须在8到32个字符之间"  />
                </el-form-item>
                <el-form-item label="角色" prop="roleId">
                  <el-select v-model="form.roleId" placeholder="请选择角色">
                    <el-option v-for="item in roleList" :key="item.id" :label="item.name" :value="item.id" />
                  </el-select>
                </el-form-item>
                <el-form-item label="手机" prop="phone">
                  <el-input  v-model="form.phone" placeholder="手机"  />
                </el-form-item>
              </div>
            </div>
          </el-form>
          <div class="popBtn">
              <el-button type="" size="small" @click="close(formRef)">取消</el-button>
              <el-button v-if="isEdit" type="primary"  size="small" @click="save(formRef)">保存</el-button>
              <el-button v-else type="primary"  size="small" @click="submit(formRef)">确定</el-button>
            </div>
        </div>
      </template>
    </Layer>
  </div>
</template>
<script lang="ts" setup>
import { ref,reactive } from 'vue'
import Layer from '@/components/Layer.vue'
import { adminAddApi,adminUpdateApi,adminInfoApi } from '@/api/api'
import {type FormInstance ,type FormRules,ElMessage } from 'element-plus'

let isEdit = ref(false)
let departmentList = ref([])

interface role {
  id:number,
  name:string
}
let roleList = ref<role[]>([])


interface RuleForm {
  account: string
  password: string
  name: string
  roleId?: number
  departmentIdList: number[]
  phone: string
}

const rules = reactive<FormRules<RuleForm>>({
  account: [
    { required: true, message: '请输入账号', trigger: 'blur' },
  ],
  // password: [
  //   { required: true, message: '请输入', trigger: 'blur' },
  // ],
  name: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  roleId: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
  departmentIdList:[
    { required: true, message: '请选择', trigger: 'change' },
  ]
})

let form = ref<RuleForm> ({
  account:'',
  name:'',
  phone:'',
  password:'',
  departmentIdList:[]
})
const emit = defineEmits(['changeData'])

const layerRef = ref()
const open = ()=>{
  layerRef.value.showModel = true
}
const close = (formEl: FormInstance | undefined)=>{
  if (!formEl) return
  formEl.resetFields()
  departmentList.value = []
  layerRef.value.showModel = false
}


const treeRef = ref()
const formRef = ref<FormInstance>()
const submit = (formEl:FormInstance | undefined )=>{
  if (!formEl) return
  form.value.departmentIdList = treeRef.value.getCheckedKeys()
  if(form.value.departmentIdList.length == 0){
    ElMessage.info('请选择部门')
    return
  }
  formEl.validate((valid) => {
    if (valid) {
      console.log(form.value);
      adminAddApi(form.value).then((res:any)=>{
        console.log(res);
        if(res.code == 0){
          ElMessage.success('成功')
          emit('changeData')
          close(formRef.value)
        }else{
          ElMessage.error(res.msg)
        }
      })
    }
  })
}

const getDetail = (id:number)=>{
  adminInfoApi(id).then((res:any)=>{
    console.log(res);
    if(res.code == 0){
      if(res.data.Departments && res.data.Departments.length > 0){
        res.data.departmentIdList = []
        res.data.Departments.forEach((item:any)=>{
          res.data.departmentIdList.push(item.id)
          if(item.children && item.children.length > 0){
            item.children.forEach((child:any)=>{
              res.data.departmentIdList.push(child.id)
              if(child.children && child.children.length > 0){
                child.children.forEach((grandson:any)=>{
                  res.data.departmentIdList.push(grandson.id) 
                })
              }
            })
          }
        })
        departmentList.value.forEach((item:any)=>{
          console.log(item)
          if(item.children && item.children.length > 0){
            if(res.data.departmentIdList.indexOf(item.id) != -1){
              res.data.departmentIdList.splice(res.data.departmentIdList.indexOf(item.id),1)
            }
            item.children.forEach((child:any)=>{
              if(child.children && child.children.length>0){
                if(res.data.departmentIdList.indexOf(child.id) != -1){
                  res.data.departmentIdList.splice(res.data.departmentIdList.indexOf(child.id),1)
                }
              }
            })
          }
        })
      }
      form.value = res.data
    }else{
      ElMessage.error(res.msg)
    }
  })
}

const save = (formEl:FormInstance | undefined)=>{
  if (!formEl) return
  form.value.departmentIdList = []
  if( treeRef.value.getCheckedNodes().length > 0){
    treeRef.value.getCheckedNodes().forEach((item:any) =>{
      form.value.departmentIdList.push(item.id)
    })
  }
  if( treeRef.value.getHalfCheckedNodes().length > 0){
    treeRef.value.getHalfCheckedNodes().forEach((item:any) =>{
      form.value.departmentIdList.push(item.id)
    })
  }
  if(form.value.departmentIdList.length == 0){
    ElMessage.info('请选择部门')
    return
  }
  formEl.validate((valid) => {
    if (valid) {
      console.log(form.value.departmentIdList.values)
      console.log(form.value);
      adminUpdateApi(form.value).then((res:any)=>{
        console.log(res);
        if(res.code == 0){
          ElMessage.success('成功')
          emit('changeData')
          close(formRef.value)
        }else{
          ElMessage.error(res.msg)
        }
      })
    }
  })
}


defineExpose({
  isEdit,
  form,
  open,
  departmentList,
  roleList,
  getDetail
})
</script>

<style lang="scss" scoped>


</style>