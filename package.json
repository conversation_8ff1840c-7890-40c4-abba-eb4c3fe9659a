{"name": "backend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "@wangeditor/plugin-upload-attachment": "^1.1.0", "axios": "^1.4.0", "crypto-js": "^4.1.1", "element-plus": "^2.3.8", "js-md5": "^0.7.3", "pinia": "^2.1.4", "pinia-plugin-persist": "^1.0.0", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@iconify-json/ep": "^1.1.10", "@types/crypto-js": "^4.1.1", "@types/js-md5": "^0.7.0", "@types/node": "^20.4.4", "@vitejs/plugin-vue": "^4.2.3", "sass": "^1.64.1", "sass-loader": "^13.3.2", "typescript": "^5.0.2", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "^4.4.5", "vue-tsc": "^1.8.5"}}