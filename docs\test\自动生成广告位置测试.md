# 自动生成广告位置功能测试

## 功能概述

修改广告位置配置为自动生成模式，不再依赖后台传入的固定配置，改为使用 `index_n` 格式的自动生成位置键，并在删除广告位置时提醒用户会同步删除该位置的所有广告。

## 主要变更

### 1. 自动生成位置键

**变更前：**
- 依赖后台 `entrancePosApi` 接口获取固定位置配置
- 用户只能选择预设的广告位置
- 位置键由后台定义

**变更后：**
- 自动生成 `index_n` 格式的位置键
- 用户可以无限添加广告位置
- 位置键自动递增，确保不重复

### 2. 删除时的广告清理

**新增功能：**
- 删除广告位置时检查该位置下的广告数量
- 提醒用户删除位置会同步删除所有广告
- 调用新的 `bannersRemoveByPosApi` 接口清理广告

### 3. 界面简化

**变更内容：**
- 移除广告位置列表显示
- 改为单一的"添加广告位置"按钮
- 简化用户操作流程

## 新增API接口

### bannersRemoveByPosApi

**接口路径：** `POST /api/entrance/deleteByPos`

**参数：**
```javascript
{
  pos: string // 位置键，如 "index_1"
}
```

**功能：** 删除指定位置下的所有广告

## 测试用例

### 1. 自动生成位置键测试

**测试步骤：**
1. 访问"网站配置 > 模块配置"
2. 点击"添加广告位置"按钮
3. 重复添加多个广告位置
4. 检查生成的位置键

**预期结果：**
- 第一个位置键为 `index_1`
- 后续位置键依次递增：`index_2`, `index_3`...
- 位置名称自动设置为 "广告位置 index_n"
- 显示成功添加提示

### 2. 位置键唯一性测试

**测试步骤：**
1. 添加几个广告位置
2. 删除中间的某个位置（如 index_2）
3. 再次添加新的广告位置
4. 检查新位置的键值

**预期结果：**
- 新添加的位置键应该是最大值+1
- 不会重用已删除的位置键
- 确保位置键的唯一性

### 3. 删除广告位置测试（无广告）

**测试步骤：**
1. 添加一个新的广告位置
2. 不在该位置添加任何广告
3. 点击删除该位置
4. 确认删除操作

**预期结果：**
- 显示删除确认对话框
- 不显示广告删除警告
- 删除成功后位置从布局中移除

### 4. 删除广告位置测试（有广告）

**测试步骤：**
1. 添加一个广告位置
2. 在该位置添加几个广告
3. 点击删除该位置
4. 检查确认对话框内容
5. 确认删除操作

**预期结果：**
- 显示删除确认对话框
- 对话框中显示广告数量警告
- 提示会同步删除所有广告
- 确认后成功删除位置和广告
- 显示删除成功提示

### 5. 删除操作取消测试

**测试步骤：**
1. 尝试删除有广告的位置
2. 在确认对话框中点击"取消"
3. 检查位置和广告是否保留

**预期结果：**
- 删除操作被取消
- 位置和广告都保持不变
- 无任何数据变更

### 6. 广告数量统计测试

**测试步骤：**
1. 在某个位置添加多个广告
2. 在布局预览中查看该位置
3. 点击配置该位置
4. 检查广告数量显示

**预期结果：**
- 布局预览中正确显示广告数量
- 配置弹窗中显示准确的广告统计
- 数量实时更新

### 7. 广告管理集成测试

**测试步骤：**
1. 添加广告位置
2. 点击"管理"按钮打开广告管理弹窗
3. 在弹窗中添加/编辑/删除广告
4. 关闭弹窗后检查广告数量更新

**预期结果：**
- 广告管理弹窗正常工作
- 广告操作后数量正确更新
- 父页面数据同步刷新

## 数据结构

### 位置配置格式

```json
{
  "positions": {
    "index_1": {
      "type": "entrance",
      "enabled": true,
      "name": "广告位置 index_1",
      "width": "full"
    },
    "index_2": {
      "type": "entrance", 
      "enabled": true,
      "name": "广告位置 index_2",
      "width": "half"
    }
  },
  "layout": ["index_1", "index_2"]
}
```

### 位置键生成逻辑

```javascript
const generateEntrancePositionKey = () => {
  const existingKeys = Object.keys(layoutConfig.positions)
    .filter(key => key.startsWith('index_'))
    .map(key => {
      const match = key.match(/index_(\d+)/)
      return match ? parseInt(match[1]) : 0
    })
  
  const maxIndex = existingKeys.length > 0 ? Math.max(...existingKeys) : 0
  return `index_${maxIndex + 1}`
}
```

## 界面变更

### 1. 可用模块区域

**变更前：**
```vue
<div v-for="pos in availableEntrancePositions" :key="pos.posKey">
  <span>{{ pos.tipTitle }}</span>
  <el-tag>{{ getEntranceCount(pos.posKey) }}个广告</el-tag>
</div>
```

**变更后：**
```vue
<div class="module-item entrance-module" @click="addEntrancePosition">
  <el-icon><Picture /></el-icon>
  <span>添加广告位置</span>
  <el-icon><Plus /></el-icon>
</div>
```

### 2. 删除确认对话框

**无广告时：**
```
确定要删除广告位置"广告位置 index_1"吗？
```

**有广告时：**
```
确定要删除广告位置"广告位置 index_1"吗？

⚠️ 注意：该位置下有 3 个广告，删除位置时会同步删除所有广告内容！
```

## 错误处理

### 1. API调用失败

- 删除广告失败时不删除位置配置
- 显示具体的错误信息
- 提供重试机制

### 2. 数据一致性

- 删除操作的原子性保证
- 失败时的回滚机制
- 数据同步的及时性

### 3. 用户操作保护

- 重要操作的二次确认
- 清晰的警告信息
- 操作结果的明确反馈

## 兼容性说明

### 1. 现有数据兼容

- 现有的 `index_1`, `index_2` 等位置键继续有效
- 新生成的位置键不会与现有键冲突
- 配置升级平滑过渡

### 2. API兼容

- 保持现有广告管理API不变
- 新增的删除接口独立实现
- 不影响其他功能模块

## 性能优化

### 1. 位置键生成

- 只在需要时计算最大索引
- 避免不必要的数组遍历
- 缓存计算结果

### 2. 数据刷新

- 精确刷新受影响的数据
- 避免全量数据重新加载
- 优化网络请求次数
