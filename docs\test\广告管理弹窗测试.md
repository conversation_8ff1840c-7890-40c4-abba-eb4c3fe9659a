# 广告管理弹窗功能测试

## 功能概述

将广告管理功能以弹窗形式集成到模块配置页面中，用户可以直接在配置页面管理对应位置的广告内容，无需跳转到其他页面。

## 新增组件

### 1. EntranceManageDialog.vue
**功能：** 广告管理主弹窗
**特点：**
- 完整的广告列表展示
- 搜索和筛选功能
- 批量操作支持
- 分页显示
- 集成广告编辑功能

### 2. EntranceEditDialog.vue
**功能：** 广告编辑弹窗
**特点：**
- 添加/编辑广告
- 图片上传功能
- 表单验证
- 状态和排序管理

## 主要功能

### 1. 弹窗式管理界面

**触发方式：**
- 配置弹窗中的"管理广告内容"按钮
- 布局预览中广告位置的"管理"按钮

**界面特点：**
- 80%宽度的大弹窗
- 完整的管理功能
- 不离开当前页面

### 2. 广告列表管理

**功能包括：**
- 按位置自动筛选
- 标题和状态搜索
- 排序拖拽调整
- 状态快速切换
- 批量删除操作

### 3. 广告编辑功能

**支持操作：**
- 添加新广告
- 编辑现有广告
- 图片上传
- 链接验证
- 状态管理

## 测试用例

### 1. 弹窗打开测试

**测试步骤：**
1. 访问"网站配置 > 模块配置"
2. 添加一个广告位置
3. 点击"配置"按钮
4. 在配置弹窗中点击"管理广告内容"

**预期结果：**
- 打开广告管理弹窗
- 弹窗标题显示位置名称
- 自动筛选该位置的广告
- 显示搜索和工具栏

### 2. 广告列表显示测试

**测试步骤：**
1. 打开广告管理弹窗
2. 检查表格数据显示
3. 测试分页功能
4. 测试搜索功能

**预期结果：**
- 表格正确显示广告数据
- 分页功能正常工作
- 搜索能正确筛选结果
- 图片正确显示

### 3. 添加广告测试

**测试步骤：**
1. 点击"添加广告"按钮
2. 填写广告信息
3. 上传广告图片
4. 提交表单

**预期结果：**
- 编辑弹窗正确打开
- 位置字段自动填充且禁用
- 图片上传功能正常
- 表单验证正确工作
- 提交成功后刷新列表

### 4. 编辑广告测试

**测试步骤：**
1. 点击某个广告的"编辑"按钮
2. 修改广告信息
3. 更换图片
4. 提交更新

**预期结果：**
- 表单正确填充现有数据
- 修改功能正常工作
- 图片更新正确
- 更新成功后刷新列表

### 5. 删除功能测试

**测试步骤：**
1. 点击单个广告的"删除"按钮
2. 确认删除操作
3. 选择多个广告进行批量删除

**预期结果：**
- 显示确认对话框
- 删除成功后刷新列表
- 批量删除功能正常
- 删除后数据正确更新

### 6. 状态和排序测试

**测试步骤：**
1. 切换广告的启用/禁用状态
2. 修改广告的排序数字
3. 检查变更是否保存

**预期结果：**
- 状态切换立即生效
- 排序修改正确保存
- 界面实时更新

### 7. 搜索和筛选测试

**测试步骤：**
1. 在标题搜索框输入关键词
2. 选择状态进行筛选
3. 点击重置按钮

**预期结果：**
- 搜索结果正确筛选
- 状态筛选正常工作
- 重置功能清空条件

## 界面设计

### 1. 管理弹窗布局

```vue
<el-dialog width="80%" title="管理广告位置：位置名称">
  <!-- 搜索区域 -->
  <div class="search-box">
    <el-form inline>
      <el-form-item label="标题">
        <el-input placeholder="请输入标题" />
      </el-form-item>
      <el-form-item label="状态">
        <el-select placeholder="请选择状态" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary">搜索</el-button>
        <el-button>重置</el-button>
      </el-form-item>
    </el-form>
  </div>

  <!-- 工具栏 -->
  <div class="toolbar">
    <el-button type="primary" icon="Plus">添加广告</el-button>
    <el-button type="danger" icon="Delete">批量删除</el-button>
  </div>

  <!-- 表格 -->
  <el-table>
    <!-- 表格列定义 -->
  </el-table>

  <!-- 分页 -->
  <el-pagination />
</el-dialog>
```

### 2. 编辑弹窗布局

```vue
<el-dialog width="600px" title="添加/编辑广告">
  <el-form>
    <el-form-item label="广告名称">
      <el-input />
    </el-form-item>
    <el-form-item label="广告链接">
      <el-input />
    </el-form-item>
    <el-form-item label="广告图片">
      <el-upload />
    </el-form-item>
    <!-- 其他表单项 -->
  </el-form>
</el-dialog>
```

## 技术实现

### 1. 弹窗组件通信

```javascript
// 主页面调用
const manageEntranceContent = (positionId: string) => {
  const positionName = getPositionName(positionId)
  entranceManageRef.value?.open(positionId, positionName)
}

// 弹窗组件暴露方法
defineExpose({
  open
})
```

### 2. 数据管理

```javascript
// 获取列表数据
const getList = async () => {
  const params = {
    page: pagination.currentPage,
    rows: pagination.pageSize,
    pos: currentPosition.value,
    ...searchForm
  }
  const res = await bannersApi(params)
}
```

### 3. 表单验证

```javascript
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入广告名称', trigger: 'blur' }
  ],
  link: [
    { required: true, message: '请输入广告链接', trigger: 'blur' },
    { type: 'url', message: '请输入正确的链接格式', trigger: 'blur' }
  ]
}
```

## 用户体验优化

### 1. 操作流畅性

- 弹窗形式避免页面跳转
- 自动筛选减少用户操作
- 实时反馈操作结果

### 2. 界面友好性

- 大弹窗提供充足操作空间
- 清晰的功能分区
- 直观的操作按钮

### 3. 数据一致性

- 操作后自动刷新列表
- 状态变更实时更新
- 错误处理和提示

## 注意事项

### 1. API接口适配

- 确保使用正确的API方法名
- 参数格式要与后端接口匹配
- 错误处理要完善

### 2. 图片上传

- 上传地址要配置正确
- 文件类型和大小限制
- 上传成功后的路径处理

### 3. 性能考虑

- 大弹窗的渲染性能
- 图片加载优化
- 分页数据管理

## 后续优化建议

1. **拖拽排序**：支持表格行拖拽调整顺序
2. **预览功能**：添加广告效果预览
3. **模板管理**：支持广告模板快速应用
4. **统计分析**：显示广告点击统计数据
