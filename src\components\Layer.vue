<template>
   <div>
    <el-dialog 
      v-model="showModel" 
      :title="title"
      center 
      width="auto"
      align-center 
      @close="close">
      <slot name="layer"></slot>
    </el-dialog>
   </div>
</template>

<script setup lang="ts">
import { ref } from "vue"

let showModel = ref(false)
const emit = defineEmits(['fatherShow'])

defineProps({
  title:String,
})

const close = ()=>{
  showModel.value = false
  emit('fatherShow')
  console.log('关闭',showModel.value);
}

defineExpose({
  showModel
})
</script>
<style lang="scss" scoped>
:deep(.el-dialog__header){
  margin: 0;
  padding: 10px;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #DEDEDE;
  .el-dialog__headerbtn{
    top: 0;
    width: 45px;
    height: 45px;
  }
}
:deep(.el-dialog.is-align-center){
  border-radius: 10px;
}
:deep(.el-dialog--center .el-dialog__body){
    padding: clamp(10px,10px + 1vw,20px) 40px !important;
    max-height: calc(100vh - 200px);
    overflow: auto;
  }
</style>
