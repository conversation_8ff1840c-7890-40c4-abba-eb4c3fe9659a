<template>
  <div>
    <h3 class="mb_2">{{$route.params.catName}}</h3>
    <div class="searchBox">
      <el-form ref="searchRef" :model="form" class="form">
        <el-form-item prop="name">
          <el-input prefix-icon="Search" v-model="form.name" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item prop="top">
          <el-select v-model="form.top" placeholder="请选择是否置顶">
            <template #prefix>
              <el-icon ><Search /></el-icon>
            </template>
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item prop="display">
          <el-select v-model="form.display" placeholder="请选择是否展示">
            <template #prefix>
              <el-icon ><Search /></el-icon>
            </template>
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getList">搜索</el-button>
          <span class="clear" @click="resetForm(searchRef)">清除选项</span>
        </el-form-item>
      </el-form>
    </div>
    <div class="toolBox">
      <!-- <el-button type="primary" icon="Plus" @click="addCat">添加</el-button> -->
    </div>
    <div>
      <el-table 
        class="table"
        :data="tableData" 
        border 
        style="width: 100%"
        :header-cell-style="{ background: '#F2F2F2', textAlign: 'center' ,color:'#666'}"
        :cell-style="{ textAlign: 'center' }"
        row-key="code"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="sort" label="排序"  width="60" />
        <el-table-column prop="title" label="标题" show-overflow-tooltip />
        <el-table-column  label="封面" width="100">
          <template #default="scope">
            <el-image v-if="scope.row.pic" style="height: 50px" :src="store.hw+scope.row.pic" />
          </template>
        </el-table-column>
        <el-table-column prop="pubtime" label="发布时间" show-overflow-tooltip />
        <el-table-column prop="endtime" label="结束时间" show-overflow-tooltip />
        <el-table-column  label="置顶" width="80">
          <template #default="scope">
            {{scope.row.top?'是':'否'}}
          </template>
        </el-table-column>
        <el-table-column  label="展示" width="80">
          <template #default="scope">
            {{scope.row.display?'是':'否'}}
          </template>
        </el-table-column>
        <el-table-column  label="驳回原因" width="80">
          <template #default="scope">
            {{scope.row.reason }}
          </template>
        </el-table-column>
        <el-table-column  label="审核" width="80">
          <template #default="scope">
            {{scope.row.audit == 1?'通过':scope.row.audit == 9?'拒绝':scope.row.audit == 0?'待审核':''}}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="270">
          <template #default="scope" >
            <div>
              <router-link :to="{name:'articleDetail',params:{'id':scope.row.id}}" target="_blank">
                <el-button class="mlr_1" type="success" size="small" plain icon="view" >预览</el-button>
              </router-link>
              <el-button v-show="scope.row.audit != 1" type="success" size="small" plain icon="check" @click="handleAudit(scope.row)">通过</el-button>
              <el-button v-show="scope.row.audit != 9" type="warning" size="small" plain icon="close" @click="handleDelAudit(scope.row)">拒绝</el-button>
              <el-button v-show="scope.row.audit != 0" type="danger" size="small" plain icon="delete" @click="handlePutAudit(scope.row)">驳回</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <PageCom ref="pageCom" :total="total" @changePage='getList' />
    </div>
    <el-dialog
      v-model="putAuditVisible"
      title="提示"
      width="30%"
      :before-close="handleClose"
    >
      <el-input
        v-model="reason"
        :rows="2"
        type="textarea"
        placeholder="请输入驳回原因(必填)"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose" size="small">取消</el-button>
          <el-button type="primary" @click="putAudit" size="small">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import PageCom from '@/components/PageCom.vue'
import { ref,reactive,onMounted,watch } from 'vue'
import { artArticleListApi,artArticleAuditApi,artArticlePutAuditApi,artArticleDelAuditApi } from '@/api/api'
import type { FormInstance } from 'element-plus'
import { useStore } from '@/store/Index'
const store = useStore()
import { useRoute,useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()

let total = ref(0)
interface RuleForm {
  name: string
  display ?: number 
  top ?: number 
}
const searchRef = ref<FormInstance>()
const form = reactive<RuleForm>({
  name:'',
})

watch(()=>router.currentRoute.value,(newValue:any)=>{
  console.log(newValue)
  getList()
})

onMounted(()=>{
  getList()
})

const pageCom = ref()
let tableData = ref([])
const getList = ()=>{
  let data = {
    page:pageCom.value.currentPage,
    rows:pageCom.value.pageSize,
    ...form,
    catId:route.params.catId
  }
  artArticleListApi(data).then((res:any) =>{
    console.log('文章列表',res)
    if(res.code == 0){
      tableData.value = res.data
      total.value = res.data.length
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  getList()
}


interface msg{
  id:number,
}

const handleAudit = (row:msg)=>{
  console.log(row)
  ElMessageBox.confirm('是否通过该文章？','提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    artArticleAuditApi(row.id).then((res:any)=>{
      if(res.code == 0){
        ElMessage.success('成功')
        getList()
      }else{
        ElMessage.error(res.msg)
      }
    })
    })
  .catch(() => {
    ElMessage.info('已取消')
  })
}
const handleDelAudit = (row:msg)=>{
  console.log(row)
  ElMessageBox.confirm('是否拒绝该文章？','提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    artArticleDelAuditApi(row.id).then((res:any)=>{
      if(res.code == 0){
        ElMessage.success('成功')
        getList()
      }else{
        ElMessage.error(res.msg)
      }
    })
    })
  .catch(() => {
    ElMessage.info('已取消')
  })
}

let putAuditVisible = ref(false)
let putAuditId = ref('')
let reason = ref('')
const putAudit = () => {
  if(!reason.value){
    ElMessage.info('请输入驳回原因！')
    return
  }
  artArticlePutAuditApi(putAuditId.value,{
    reason:reason.value
  }).then((res:any)=>{
    if(res.code == 0){
      ElMessage.success('成功')
      handleClose()
      getList()
    }else{
      ElMessage.error(res.msg)
    }
  })
} 
const handlePutAudit = (row:msg)=>{
  console.log(row)
  putAuditVisible.value= true
  putAuditId.value = String(row.id)
}
const handleClose = ()=>{
  putAuditVisible.value= false
  reason.value = ''
  putAuditId.value = ''
}

</script>
<style lang="scss" scoped>
h3{
  border-bottom: 1px solid #e5e6eb ;
}

</style>