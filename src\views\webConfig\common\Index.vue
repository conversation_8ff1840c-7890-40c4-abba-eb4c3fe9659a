<template>
  <div>
    <div class="toolBox">
      <el-button type="primary" icon="FolderChecked" @click="save(formRef)">保存</el-button>
    </div>
    <div>
      <el-form ref="formRef" :model="form" class="form" label-width="auto" :rules="rules">
        <el-form-item label="网站名称" prop="title">
          <el-input v-model="form.title" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="关键词" prop="keywords">
          <el-input v-model="form.keywords" placeholder="请输入关键词" />
        </el-form-item>
        <el-form-item label="描述" prop="brief">
          <el-input v-model="form.brief" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="网站LOGO" prop="logo">
          <el-upload
            accept=".png,.jpg,.jpeg"
            :file-list="fileList"
            action=""
            :multiple="false"
            :before-upload="beforeUpload"
            :before-remove="beforeRemove"
            :on-change="fileChange"
            :on-exceed="handleExceed"
            :limit="1"
            :with-credentials="true"
          >
            <el-button v-if="!form.logo" type="primary" size="small">上传图片</el-button>
            <div v-else>
              <el-image  style="height: 100px;"  :src="store.hw + form.logo"  fit="fill" />
              <el-icon @click.stop="form.logo = ''"><CircleClose /></el-icon>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="地址栏图标" prop="icon">
          <el-upload
            accept=".png,.jpg,.jpeg"
            :file-list="fileList1"
            action=""
            :multiple="false"
            :before-upload="beforeUpload1"
            :before-remove="beforeRemove1"
            :on-change="fileChange1"
            :on-exceed="handleExceed"
            :limit="1"
            :with-credentials="true"
          >
            <el-button v-if="!form.icon" type="primary" size="small">上传图片</el-button>
            <div v-else>
              <el-image  style="height: 100px"     :src="store.hw + form.icon"  fit="fill" />
              <el-icon @click.stop="form.icon = ''"><CircleClose /></el-icon>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="备案号" prop="icp">
          <el-input v-model="form.icp" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="公安网备号" prop="gongan">
          <el-input v-model="form.gongan" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="技术支持" prop="support">
          <el-input v-model="form.support" placeholder="请输入技术支持" />
        </el-form-item>
        <el-form-item label="版权所有" prop="copyright">
          <el-input v-model="form.copyright" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="联系电话" prop="tel">
          <el-input v-model="form.tel" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="联系地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入" />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref,reactive,onMounted } from 'vue'
import { comConfigFindApi,comConfindSaveApi,uploadApi } from '@/api/api'
import {type FormInstance ,type FormRules,ElMessage ,type UploadProps, UploadUserFile} from 'element-plus'
import { useStore } from '@/store/Index';
const store = useStore()

interface RuleForm {
  title: string
  logo: string
  keywords: string
  icp: string
  support: string
  gongan: string
  brief: string
  icon: string
  copyright: string
  address: string
  tel: string
}
const formRef = ref<FormInstance>()
let form = ref<RuleForm>({
  address:'',
  title:'',
  logo:'',
  keywords:'',
  icp:'',
  gongan:'',
  support:'',
  brief:'',
  icon:'',
  copyright:'',
  tel:''
})

const rules = reactive<FormRules<RuleForm>>({
  title: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  logo: [
    { required: true, message: '请上传', trigger: 'blur' },
  ],
  icp: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
})

onMounted(()=>{
  getList()
})

let tableData = ref([])
const getList = ()=>{
  let data = {
    type:'web'
  }
  comConfigFindApi(data).then((res:any) =>{
    console.log('公共配置',res)
    if(res.code == 0 && res.data){
      form.value = res.data
    }
  })
}

const save = (formEl:FormInstance | undefined)=>{
  console.log(tableData.value)
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      console.log(form.value);
      comConfindSaveApi({
        type:'web',
        data:JSON.stringify(form.value)
      }).then((res:any)=>{
        console.log(res);
        if(res.code == 0){
          ElMessage.success('成功')
          getList()
        }else{
          ElMessage.error(res.msg)
        }
      })
    }
  })
}


// 上传
let fileList = ref<UploadUserFile[]>([])
const handleExceed: UploadProps['onExceed'] = ()=>{
  ElMessage.warning('当前限制选择1个文件')
}
const fileChange: UploadProps['onChange'] = (file)=>{
  fileList.value = [file]
}
const beforeRemove: UploadProps['beforeRemove'] = (file, fileLists)=>{
  console.log(file, fileLists);
  fileList.value  = []
  return true
}

const beforeUpload: UploadProps['beforeUpload'] = (file)=>{
  console.log('文件',file);
  const isLt100M = file.size / 1024 / 1024 < 10;
  if (!isLt100M) {
    ElMessage.warning("大小不得超过10M");
    return false;
  }
  let params = new FormData();
  params.append("file", file);
  params.append("isTemp","forever");
  params.append("uType","admin");
  uploadApi(params).then((res:any)=>{
    console.log(res);
    if(res.code == 0){
      form.value.logo =  res.data
    }else{
      ElMessage.error(res.msg)
    }
  })
  return false;
}
// 上传
let fileList1 = ref<UploadUserFile[]>([])
const fileChange1: UploadProps['onChange'] = (file)=>{
  fileList1.value = [file]
}
const beforeRemove1: UploadProps['beforeRemove'] = (file, fileLists)=>{
  console.log(file, fileLists);
  fileList1.value  = []
  return true
}

const beforeUpload1: UploadProps['beforeUpload'] = (file)=>{
  console.log('文件',file);
  const isLt100M = file.size / 1024 / 1024 < 10;
  if (!isLt100M) {
    ElMessage.warning("大小不得超过10M");
    return false;
  }
  let params = new FormData();
  params.append("file", file);
  params.append("isTemp","forever");
  params.append("uType","admin");
  uploadApi(params).then((res:any)=>{
    console.log(res);
    if(res.code == 0){
      form.value.icon =  res.data
    }else{
      ElMessage.error(res.msg)
    }
  })
  return false;
}

</script>

<style lang="scss" scoped>
.form{
  width: 600px;
}
</style>