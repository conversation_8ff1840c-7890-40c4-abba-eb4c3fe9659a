<template>
  <div>
    <Layer ref="layerRef" title="修改信息"  @fatherShow='close'>
      <template #layer>
        <div>
          <el-form ref="formRef" :model="form" :rules="rules" label-width="auto">
            <el-form-item label="新密码" prop="password">
              <el-input  v-model="form.password" show-password placeholder="新密码" type="password" />
            </el-form-item>
            <el-form-item label="再次输入" prop="repassword">
              <el-input  v-model="form.repassword" show-password placeholder="再次输入" type="password" />
            </el-form-item>
          </el-form>
          <div class="popBtn">
              <el-button type="" size="small" @click="close(formRef)">取消</el-button>
              <el-button type="primary"  size="small" @click="save(formRef)">保存</el-button>
            </div>
        </div>
      </template>
    </Layer>
  </div>
</template>
<script lang="ts" setup>
import { ref,reactive } from 'vue'
import Layer from '@/components/Layer.vue'
import {adminChangePwdApi } from '@/api/api'
import {type FormInstance ,type FormRules,ElMessage } from 'element-plus'
import router from '@/router/Index';
import { useStore } from '@/store/Index'

const store = useStore()

interface RuleForm {
  oldPassword: string
  password: string
  repassword: string
}

const rules = reactive<FormRules<RuleForm>>({
  oldPassword: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  repassword: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
})

let form = ref<RuleForm> ({
  oldPassword:'',
  password:'',
  repassword:''
})

const layerRef = ref()
const open = ()=>{
  layerRef.value.showModel = true
}
const close = (formEl: FormInstance | undefined)=>{
  if (!formEl) return
  formEl.resetFields()
  layerRef.value.showModel = false
}


const formRef = ref<FormInstance>()
const save = (formEl:FormInstance | undefined)=>{
  if (!formEl) return
  
  formEl.validate((valid) => {
    if (valid) {
      if(form.value.password !== form.value.repassword){
        ElMessage.info('新密码输入不一致！')
        return
      }
      console.log(form.value);
      adminChangePwdApi(form.value).then((res:any)=>{
        console.log(res);
        if(res.code == 0){
          ElMessage.success('成功,请重新登录')
          close(formRef.value)
          store.$reset();
          router.push('/login')
          
        }else{
          ElMessage.error(res.msg)
        }
      })
    }
  })
}

defineExpose({
  open,
})
</script>

<style lang="scss" scoped>


</style>