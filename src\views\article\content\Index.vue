<template>
  <div class="flex">
    <div class="type_box">
      <h3>分类列表</h3>
      <el-tree  :data="tableData" :props="defaultProps" @node-click="handleNodeClick" >
        <template #default="{ node, data }">
          <span class="custom-tree-node">
            <span> 
              <el-icon v-if="data.type == 2"><Memo /></el-icon>
              <el-icon v-else><Document /></el-icon>
              {{ node.label }}
            </span>
          </span>
        </template>
      </el-tree>
    </div>
    <div class="type_con">
      <router-view></router-view>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref,onMounted } from 'vue'
import { articleCatListApi } from '@/api/api'
import { useRouter } from 'vue-router'
const router = useRouter()


const defaultProps = {
  children: 'children',
  label: 'title',
}

interface Tree {
  label: string
  children?: Tree[]
  type:string
  id:number
  title:string
  modelId:number,
  pid:number
}

const handleNodeClick = (data: Tree) => {
  console.log(data)
  if(data.type == 'list'){
    router.push({
      name:'contentList',
      params:{
        catId:data.id,
        catName:data.title,
      }
    })
  }else{
    router.push({
      name:'contentIndex',
      params:{
        catId:data.id,
        catName:data.title,
      }
    })
  }
}


onMounted(()=>{
  getList()
  
})

let tableData = ref([])
const getList = ()=>{
  let data = {
    page:1,
    rows:999,
  }
  articleCatListApi(data).then((res:any) =>{
    console.log('文章分类管理',res)
    if(res.code == 0){
      tableData.value = res.data
    }
  })
}

</script>

<style lang="scss" scoped>
.type_box{
  width: 16%;
  border-right: 5px solid #eee;
  padding-right: 10px;
  h3{
    border-bottom: 1px solid #e5e6eb ;
  }
  .el-tree{
    padding: 20px 0px;
  }
  :deep(.el-tree-node__content,.el-tree-node){
    margin-bottom: 10px;
  }
}
.type_con{
  width: 80%;
}
</style>