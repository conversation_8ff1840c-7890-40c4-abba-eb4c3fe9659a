/**
  initValue: 父组件传递过来的富文本框初始值，这里会实时监听，更新初始值，放置在弹窗中使用，没有钩子函数的更新。
  getEditorContent() 方法，父组件通过这个方法获取富文本编辑器的内容，包括数组格式的和html格式的内容
*/
<template>
  <div style="border: 1px solid #ccc;">
    <Toolbar
      style="border-bottom: 1px solid #ccc"
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      :mode="'simple'"
    />
    <Editor
      class="editor"
      style="height: 310px;width: 100%;"
      v-model="valueHtml"
      :defaultConfig="editorConfig"
      :mode="'simple'"
      @onCreated="handleCreated"
      @onChange="handleChange"
      @keydown.enter="keyDown"
    />
  </div>
</template>
<script lang="ts" setup>
import '@wangeditor/editor/dist/css/style.css' // 引入 css
import { onBeforeUnmount,onMounted,nextTick, ref, shallowRef, watch } from 'vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { IEditorConfig,IToolbarConfig } from '@wangeditor/editor'

import { uploadApi } from '@/api/api'
import { useStore } from '@/store/Index';
const store = useStore()


watch(()=>props.initValue, (value) => {  // 父组件获取初始值
  // console.log('监听',value)
  valueHtml.value = value
})
// 初始值
const props = defineProps({
  initValue: String
})


// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef()
// 内容 HTML
const valueHtml = ref(props.initValue)
// 模拟 ajax 异步获取内容
onMounted(() => {
  // 界面节点更新完以后再修改值。
   nextTick(() => {
     valueHtml.value = props.initValue
   })
})

// 工具栏配置
const toolbarConfig: Partial<IToolbarConfig> = {
  toolbarKeys: [
    // 菜单 key
    'headerSelect',
    'bold', // 加粗
    'italic', // 斜体
    'through', // 删除线
    'underline', // 下划线
    'bulletedList', // 无序列表
    'numberedList', // 有序列表
    'color', // 文字颜色
    'insertLink', // 插入链接
    'fontSize', // 
    'fontFamily',  // 字体
    'lineHeight', // 行高
    'uploadImage', // 上传图片
    // 'insertImage', // 网络图片
    // 'deleteImage',//删除图片
    'delIndent', // 缩进
    'indent', // 增进
    'divider', // 分割线
    'insertTable', // 插入表格
    'justifyCenter', // 居中对齐
    'justifyJustify', // 两端对齐
    'justifyLeft', // 左对齐
    'justifyRight', // 右对齐
    'undo', // 撤销
    'redo', // 重做
    'clearStyle', // 清除格式
    'fullScreen', // 全屏
  ],
  insertKeys: {
    index: 12, // 自定义插入的位置
    keys: ['uploadAttachment'], // “上传附件”菜单
  },
}
const editorConfig: Partial<IEditorConfig> = {
  placeholder: '请输入内容...', // 配置默认提示
  
  // 在编辑器中，点击选中“附件”节点时，要弹出的菜单
  hoverbarKeys: {
    attachment: {
      menuKeys: ['downloadAttachment'], // “下载附件”菜单
    },
    'image':{
      menuKeys:[
        'imageWidth30',
        'imageWidth50',
        'imageWidth60',
        'imageWidth70',
        'imageWidth80',
        'imageWidth100',
        'viewImageLink',
        'editImage',
        'deleteImage',
      ]
    }
  },
  MENU_CONF: {                // 配置上传服务器地址
    uploadImage: {
      // 小于该值就插入 base64 格式（而不上传），默认为 0
      base64LimitSize: 5 * 1024, // 5kb
      // 单个文件的最大体积限制，默认为 2M
      // maxFileSize: 1 * 1024 * 1024, // 1M
      // // 最多可上传几个文件，默认为 100
      // maxNumberOfFiles: 5,
      // 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
      allowedFileTypes: ['image/*'],
      // 自定义上传
      async customUpload(file:File, insertFn:Function) { // 文件上传
        const formData = new FormData();
        formData.append("file", file);
        formData.append("isTemp","forever");
        formData.append("uType","admin");

        // 这里根据自己项目封装情况，设置上传地址
        let result = await uploadApi(formData).then((res:any)=>{
          console.log(res,'上传')
          if(res.code == 0){
            ElMessage.success(res.msg)
            return  res.data
          }else{
            ElMessage.error(res.msg);
          }
        })
         // 插入到富文本编辑器中，主意这里的三个参数都是必填的，要不然控制台报错：typeError: Cannot read properties of undefined (reading 'replace')
        insertFn(store.hw + result, result, result)
      }
    },
    // “上传附件”菜单的配置
    uploadAttachment: {
      maxFileSize: 10 * 1024 * 1024, // 10M
      // 用户自定义上传
      async customUpload(file: File, insertFn: Function) {
        const formData = new FormData();
        formData.append("file", file);
        formData.append("isTemp","forever");
        formData.append("uType","admin");

        // 这里根据自己项目封装情况，设置上传地址
        let result = await uploadApi(formData).then((res:any)=>{
          console.log(res,'上传')
          if(res.code == 0){
            ElMessage.success(res.msg)
            return  res.data
          }else{
            ElMessage.error(res.msg);
          }
        })
        console.log(result,'上传文件')
        insertFn(`${file.name}`, store.hw + result)
      },
    },
  }
}

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return
  editor.destroy()
})
const handleCreated = (editor:any) => {
  editorRef.value = editor // 创建富文本编辑器
}

const emits = defineEmits(['getEditorContent'])
const handleChange = (info:any) => {
  emits('getEditorContent', info.getHtml())
}

// 回车换行问题
const keyDown = (e:any) => {
  console.log(e)
  if(e.keyCode === 13){
    e.preventDefault();   //阻止之前的动作
    editorRef.value.insertBreak();
  }
}

</script>