import { createApp } from 'vue'
import './style.scss'
import App from './App.vue'
import router from './router/Index'
import btn from './api/btn'
import { createPinia } from 'pinia'
import piniaPersist from 'pinia-plugin-persist'
const pinia = createPinia()
pinia.use(piniaPersist)

// 富文本附件上传注册
import { Boot } from '@wangeditor/editor'
import attachmentModule from '@wangeditor/plugin-upload-attachment'
Boot.registerModule(attachmentModule)
// 富文本图片添加大小注册菜单
import {
  imageWidth60MenuConf,
  imageWidth70MenuConf,
  imageWidth80MenuConf,
} from './api/wangeditor/index'

Boot.registerMenu(imageWidth60MenuConf);
Boot.registerMenu(imageWidth70MenuConf);
Boot.registerMenu(imageWidth80MenuConf);

// 如果您正在使用CDN引入，请删除下面一行。
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

const app = createApp(App)
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(router).use(btn).use(pinia).mount('#app')
