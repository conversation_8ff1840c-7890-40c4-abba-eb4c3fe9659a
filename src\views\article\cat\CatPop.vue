<template>
  <div>
    <Layer ref="layerRef" :title="isEdit?'修改':'添加'"  @fatherShow='close'>
      <template #layer>
        <div>
          <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
            <div class="flex">
              <div>
                <el-form-item label="类型" prop="type">
                  <el-radio-group v-model="form.type" :disabled="false">
                    <el-radio label="page">单页</el-radio>
                    <el-radio label="list">列表</el-radio>
                    <el-radio label="link">外链</el-radio>
                  </el-radio-group>
                </el-form-item>
                
                <!-- <el-form-item label="父ID" prop="pid">
                  <el-input disabled v-model="form.pid" placeholder="父ID" type="number" />
                </el-form-item> -->
                <el-form-item label="分类名称" prop="title">
                  <el-input  v-model="form.title" placeholder="分类名称"  />
                </el-form-item>
                <el-form-item label="副标题" prop="subtitle">
                  <el-input  v-model="form.subtitle" placeholder="副标题"  />
                </el-form-item>
                <el-form-item label="外链链接" prop="link">
                  <el-input  v-model="form.link" placeholder="类型为外链时必填"  />
                </el-form-item>
                <el-form-item label="外链弹出方式" prop="target">
                  <el-radio-group v-model="form.target" :disabled="false">
                    <el-radio :label="0">当前</el-radio>
                    <el-radio :label="1">新窗口</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="每页数量" prop="rows">
                  <el-input  v-model="form.rows" placeholder="每页数量"  />
                </el-form-item>
                <el-form-item label="是否展示" prop="display">
                  <el-radio-group v-model="form.display">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="排序" prop="sort">
                  <el-input  v-model="form.sort" placeholder="排序" type="number" />
                </el-form-item>
                <el-form-item label="关键字" prop="keywords">
                  <el-input  v-model="form.keywords" placeholder="关键字"  />
                </el-form-item>
                <el-form-item label="简介" prop="description">
                  <el-input  v-model="form.description" placeholder="简介" type="textarea"   />
                </el-form-item>
                <el-form-item label="模型列表" prop="modelId">
                  <el-select v-model="form.modelId" placeholder="请选择模型" :disabled="isEdit">
                    <el-option v-for="item in modelArr" :label="item.name" :value="item.id" />
                  </el-select>
                </el-form-item>
                <el-form-item label="图标" prop="icon">
                  <el-upload
                    accept=".png,.jpg,.jpeg"
                    :file-list="fileList"
                    action=""
                    :multiple="false"
                    :before-upload="beforeUpload"
                    :before-remove="beforeRemove"
                    :on-change="fileChange"
                    :on-exceed="handleExceed"
                    :limit="1"
                    :with-credentials="true"
                  >
                    <el-button v-if="!form.icon" type="primary" size="small">上传</el-button>
                    <div v-else>
                      <el-image  style="max-width: 200px;height: 100px"     :src="store.hw + form.icon"  fit="fill" />
                      <el-icon @click.stop="form.icon = ''"><CircleClose /></el-icon>
                    </div>
                  </el-upload>
                </el-form-item>
                <el-form-item label="大图" prop="pic">
                  <el-upload
                    accept=".png,.jpg,.jpeg"
                    :file-list="fileList1"
                    action=""
                    :multiple="false"
                    :before-upload="beforeUpload1"
                    :before-remove="beforeRemove1"
                    :on-change="fileChange1"
                    :on-exceed="handleExceed"
                    :limit="1"
                    :with-credentials="true"
                  >
                    <el-button v-if="!form.pic" type="primary" size="small">上传</el-button>
                    <div v-else>
                      <el-image  style="max-width: 200px;height: 100px"     :src="store.hw + form.pic"  fit="fill" />
                      <el-icon @click.stop="form.pic = ''"><CircleClose /></el-icon>
                    </div>
                  </el-upload>
                </el-form-item>
              </div>
            </div>
          </el-form>

          <div class="popBtn">
              <el-button type="" size="small" @click="close()">取消</el-button>
              <el-button v-if="isEdit" type="primary"  size="small" @click="save(formRef)">保存</el-button>
              <el-button v-else type="primary"  size="small" @click="submit(formRef)">确定</el-button>
            </div>
        </div>
      </template>
    </Layer>
  </div>
</template>
<script lang="ts" setup>
import Layer from '@/components/Layer.vue'
import { ref,reactive } from 'vue'
import { articleCatAddApi,articleCatUpdateApi,uploadApi } from '@/api/api'
import {type FormInstance ,type FormRules,ElMessage, type UploadProps, UploadUserFile} from 'element-plus'
import { useStore } from '@/store/Index'

const store = useStore()
let isEdit = ref(false)

interface modelType {
  id :number
  name : string
}

let modelArr:Ref<modelType[]> = ref([])

const layerRef = ref()
const rules = reactive<FormRules>({
  title: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  type: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
  sort: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  display: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
})
let form = ref ({
  pid:0,
  sort:0,
  title:'',
  subtitle:'',
  type:'page',
  link:'',
  icon:'',
  pic:'',
  rows:10,
  display:1,
  keywords:'',
  description:'',
  target:1,
  modelId:''
})
const emit = defineEmits(['changeData'])


const open = ()=>{
  layerRef.value.showModel = true
}
const close = ()=>{
  form.value = {
    pid:0,
    sort:0,
    title:'',
    subtitle:'',
    type:'page',
    link:'',
    icon:'',
    pic:'',
    rows:10,
    display:1,
    keywords:'',
    description:'',
    target:1,
    modelId:''
  }
  console.log(form.value)
  layerRef.value.showModel = false
}


const formRef = ref<FormInstance>()
const submit = (formEl:FormInstance | undefined )=>{
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      console.log(form.value);
      articleCatAddApi(form.value).then((res:any)=>{
        console.log(res);
        if(res.code == 0){
          ElMessage.success('成功')
          emit('changeData')
          close()
        }else{
          ElMessage.error(res.msg)
        }
      })
    }
  })
}

const save = (formEl:FormInstance | undefined)=>{
  console.log(form.value);
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      console.log(form);
      articleCatUpdateApi(form.value).then((res:any)=>{
        console.log(res);
        if(res.code == 0){
          ElMessage.success(res.msg)
          emit('changeData')
          close()
        }else{
          ElMessage.error(res.msg)
        }
      })
    }
  })
}

// 上传
let fileList = ref<UploadUserFile[]>([])
let fileList1 = ref<UploadUserFile[]>([])
const handleExceed: UploadProps['onExceed'] = ()=>{
  ElMessage.warning('当前限制选择1个文件')
}
const fileChange: UploadProps['onChange'] = (file)=>{
  fileList.value = [file]
}
const fileChange1: UploadProps['onChange'] = (file)=>{
  fileList1.value = [file]
}
const beforeRemove: UploadProps['beforeRemove'] = (file, fileLists)=>{
  console.log(file, fileLists);
  fileList.value  = []
  return true
}
const beforeRemove1: UploadProps['beforeRemove'] = (file, fileLists)=>{
  console.log(file, fileLists);
  fileList1.value  = []
  return true
}
const beforeUpload: UploadProps['beforeUpload'] = (file)=>{
  console.log('文件',file);
  const isLt100M = file.size / 1024 / 1024 < 10;
  if (!isLt100M) {
    ElMessage.warning("大小不得超过10M");
    return false;
  }
  let params = new FormData();
  params.append("file", file);
  params.append("isTemp","forever");
  params.append("uType","admin");
  
  uploadApi(params).then((res:any)=>{
    console.log(res);
    if(res.code == 0){
      form.value.icon =  res.data
    }else{
      ElMessage.error(res.msg)
    }
  })
  return false;
}
const beforeUpload1: UploadProps['beforeUpload'] = (file)=>{
  console.log('文件',file);
  const isLt100M = file.size / 1024 / 1024 < 10;
  if (!isLt100M) {
    ElMessage.warning("大小不得超过10M");
    return false;
  }
  let params = new FormData();
  params.append("file", file);
  params.append("isTemp","forever");
  params.append("uType","admin");
  uploadApi(params).then((res:any)=>{
    console.log(res);
    if(res.code == 0){
      form.value.pic =  res.data
    }else{
      ElMessage.error(res.msg)
    }
  })
  return false;
}
defineExpose({
  isEdit,
  form,
  open,
  modelArr
})
</script>

<style lang="scss" scoped>


</style>