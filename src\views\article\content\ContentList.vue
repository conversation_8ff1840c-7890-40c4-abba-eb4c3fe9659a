<template>
  <div>
    <h3 class="mb_2">{{$route.params.catName}}</h3>
    <div class="searchBox">
      <el-form ref="searchRef" :model="form" class="form">
        <el-form-item prop="name">
          <el-input prefix-icon="Search" v-model="form.name" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item prop="status">
          <el-select v-model="form.status" placeholder="请选择状态">
            <template #prefix>
              <el-icon ><Search /></el-icon>
            </template>
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item prop="auditStatus">
          <el-select v-model="form.auditStatus" placeholder="请选择审核状态">
            <template #prefix>
              <el-icon ><Search /></el-icon>
            </template>
            <el-option label="待审核" value="0" />
            <el-option label="通过" value="1" />
            <el-option label="驳回" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getList">搜索</el-button>
          <span class="clear" @click="resetForm(searchRef)">清除选项</span>
        </el-form-item>
      </el-form>
    </div>
    <div class="toolBox">
      <el-button type="primary" icon="Plus" @click="addCat">添加</el-button>
    </div>
    <div>
      <el-table 
        class="table"
        :data="tableData" 
        border 
        style="width: 100%"
        :header-cell-style="{ background: '#F2F2F2', textAlign: 'center' ,color:'#666'}"
        :cell-style="{ textAlign: 'center' }"
        row-key="code"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="sort" label="排序"  width="60" />
        <el-table-column prop="title" label="标题" show-overflow-tooltip />
        <el-table-column  label="封面" width="100">
          <template #default="scope">
            <el-image v-if="scope.row.pic" style="height: 50px" :src="store.hw + scope.row.pic" />
          </template>
        </el-table-column>
        <el-table-column prop="pubtime" label="发布时间" show-overflow-tooltip />
        <el-table-column prop="endtime" label="结束时间" show-overflow-tooltip />
        <el-table-column  label="置顶" width="80">
          <template #default="scope">
            {{scope.row.top?'是':'否'}}
          </template>
        </el-table-column>
        <el-table-column  label="展示" width="80">
          <template #default="scope">
            {{scope.row.display?'是':'否'}}
          </template>
        </el-table-column>
        <el-table-column  label="驳回原因" width="80">
          <template #default="scope">
            {{scope.row.reason }}
          </template>
        </el-table-column>
        <el-table-column  label="审核" width="80">
          <template #default="scope">
            {{scope.row.audit == 1?'通过':scope.row.audit == 9?'拒绝':scope.row.audit == 0?'待审核':''}}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="270">
          <template #default="scope" >
            <div>
              <el-button v-btn type="info" size="small" plain icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
              <router-link :to="{name:'articleDetail',params:{'id':scope.row.id}}" target="_blank">
                <el-button class="mlr_1" type="success" size="small" plain icon="view" >预览</el-button>
              </router-link>
              <el-button v-btn type="danger" size="small" plain icon="Delete" @click="handleDel(scope.row.id)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <PageCom ref="pageCom" :total="total" @changePage='getList' />
    </div>
    <ContentPop ref="contentPop" @changeData="getList" />
  </div>
</template>
<script lang="ts" setup>
import PageCom from '@/components/PageCom.vue'
import ContentPop from './ContentPop.vue'
import { ref,reactive,onMounted,watch } from 'vue'
import { artArticleListApi,artArticleRemoveApi,artArticleInfoApi } from '@/api/api'
import type { FormInstance } from 'element-plus'
import { useStore } from '@/store/Index'
const store = useStore()
import { useRoute,useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()

let total = ref(0)
interface RuleForm {
  name: string
  status: string 
  auditStatus: string 
}
const searchRef = ref<FormInstance>()
const form = reactive<RuleForm>({
  name:'',
  status:'',
  auditStatus:''
})

watch(()=>router.currentRoute.value,(newValue:any)=>{
  console.log(newValue)
  getList()
})

onMounted(()=>{
  getList()
})

const pageCom = ref()
let tableData = ref([])
const getList = ()=>{
  let data = {
    page:pageCom.value.currentPage,
    rows:pageCom.value.pageSize,
    ...form,
    catId:route.params.catId
  }
  artArticleListApi(data).then((res:any) =>{
    console.log('文章列表',res)
    if(res.code == 0){
      tableData.value = res.data
      total.value = res.data.length
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  getList()
}


interface msg{
  id:number,
  status:boolean
  code:number
}

const contentPop = ref()
const addCat = ()=>{
  contentPop.value.open()
  contentPop.value.getTypeList()
  contentPop.value.form.catId = Number(route.params.catId)
  contentPop.value.isEdit = false
}
const handleEdit = (row:msg)=>{
  artArticleInfoApi(row.id).then((res:any)=>{
    console.log('详情',res)
    if(res.code == 0){
      contentPop.value.open()
      contentPop.value.getTypeList()
      contentPop.value.isEdit = true
      contentPop.value.form = res.data
    }else{
      ElMessage.error(res.msg)
    }
  })
}

const handleDel = (id:number)=>{
  ElMessageBox.confirm('是否删除该数据？','提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'error',
    }
  ).then(() => {
    artArticleRemoveApi(id).then((res:any)=>{
      if(res.code == 0){
        ElMessage.success('成功')
        getList()
      }else{
        ElMessage.error(res.msg)
      }
    })
    })
  .catch(() => {
    ElMessage.info('已取消')
  })
}

// const handleAudit = (row:msg,val:number)=>{
//   console.log(row,val)
//   ElMessageBox.confirm('是否修改审核状态？','提示',
//     {
//       confirmButtonText: '确认',
//       cancelButtonText: '取消',
//       type: 'warning',
//     }
//   ).then(() => {
//     artArticleAuditApi({
//       id:row.id,
//       auditStatus:val
//     }).then((res:any)=>{
//       if(res.code == 0){
//         ElMessage.success('成功')
//         getList()
//       }else{
//         ElMessage.error(res.msg)
//       }
//     })
//     })
//   .catch(() => {
//     ElMessage.info('已取消')
//   })
// }

</script>
<style lang="scss" scoped>
h3{
  border-bottom: 1px solid #e5e6eb ;
}

</style>