<template>
  <div class="login">
    <div class="box">
      <h1>后台管理登录</h1>
      <el-form
        class="formBox"
        ref="ruleFormRef"
        :model="ruleForm"
        :rules="rules"
        size="large"
      >
        <el-form-item prop="account">
          <el-input v-model="ruleForm.account" placeholder="请输入用户名/账号" clearable>
          <template #prefix>
            <el-icon><User /></el-icon>
          </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input v-model="ruleForm.password" type="password" placeholder="请输入密码"
 show-password clearable  >
            <template #prefix>
              <el-icon><Unlock /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="captcha">
          <div class="codeBox">
            <el-input class="input" v-model="ruleForm.captcha" placeholder="请输入验证码" clearable >
              <template #prefix>
                <el-icon><Position /></el-icon>
              </template>
            </el-input>
            <el-image class="img" :src="codeSrc" fit="fill" @click="changeCode" />
          </div>
        </el-form-item>
        <el-form-item >
          <el-button class="btn" size="default" type="primary" round @click="submitForm(ruleFormRef)">
            登录
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted,reactive,ref,onUnmounted } from 'vue';
import { useStore } from '@/store/Index';
import { loginApi } from '@/api/api';
import {type FormInstance, type FormRules } from 'element-plus'
import router from '@/router/Index';

const store = useStore()

const ruleForm = reactive({
  account: '',
  password: '',
  captcha: '',
  type:'account'
})

// const ruleForm = reactive({
//   account: 'adminer',
//   password: 'PrssWard123',
//   captcha: '',
//   type:'account'
// })

const rules = reactive<FormRules>({
  account: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{required: true,message: '请输入密码',trigger: 'blur',},
  ],
  captcha: [{required: true,message: '请输入验证码',trigger: 'blur',}]
})

onMounted(()=>{

  document.onkeydown = (e) => {
    if (e.keyCode == 13) {
      submitForm(ruleFormRef.value)
    }
  };
  changeCode()
})
onUnmounted(()=>{
  document.onkeydown = (e) => {
    if (e.keyCode == 13) {
      return false;
    }
  }
})

// 验证码
let codeSrc = ref('')
const changeCode = ()=>{
  ruleForm.captcha = ''
  codeSrc.value = store.base_url + "/api/code/gen?uType=admin&token=" + Math.random()
}

// 判断设备
const getDevice = ()=>{
  const u = navigator.userAgent;
  if (!!u.match(/compatible/i) || u.match(/Windows/i)) {
    return 'windows'
  } else if (!!u.match(/Macintosh/i) || u.match(/MacIntel/i)) {
    return 'macOS'
  } else if (!!u.match(/iphone/i) || u.match(/Ipad/i)) {
    return 'ios'
  } else if (u.match(/android/i)) {
    return 'android'
  } else if (u.match(/Ubuntu/i)) {
    return 'Ubuntu'
  } else {
    return 'other'
  }
  
}

const ruleFormRef = ref<FormInstance>()
const submitForm = (formEl:FormInstance | undefined )=>{
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      console.log('submit!',ruleForm)
      loginApi({
        ...ruleForm,
        device:getDevice()
      }).then((res:any) =>{
        console.log(res)
        if(res.code == 0){
          ElMessage.success('登录成功！')
          store.changeUser(res.data)
          router.push('/home')
        }else{
          ElMessage.error(res.msg)
          changeCode()
        }
      })
    } else {
      ElMessage.info('请填写完整信息！')
    }
  })
}
</script>

<style lang="scss" scoped>
.login{
  width: 100vw;
  height: 100vh;
  background: url(@/assets/login_bg.png) no-repeat;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  .box{
    background-color: #fff;
    padding: 20px 30px 0px;
    border-radius: 10px;
    h1{
      font-size: 20px;
      text-align: center;
      margin-bottom: 20px;
    }
  }
  .formBox{
    width: 300px;
    .codeBox{
      display: flex;
      align-items: center;
      justify-content: space-between;
      .input{
        width: 60%;
      }
      .img{
        width:38% ;
        height: 40px;
        cursor: pointer;
      }
    }
    .btn{
      width: 100%;
      margin: 10px auto;
    }
  }
}
</style>