import { defineStore } from 'pinia'

let base_url:string = ''
if (import.meta.env.MODE == 'development') {
    base_url = 'https://cms.test.jijiaox.com/';
} else {
    base_url = window.location.protocol+'//' + window.location.host+'/';
}
console.log(base_url)

interface user {
  name:string
  nicker:string
  avatar:string
  email:string
  phone:string
  id:number
}

interface routerRef {
  name:string
  path:string
}

export const useStore  = defineStore('main',{
  state:()=>{
    return {
      base_url:base_url,
      hw:base_url+'api/file/preview/',
      userInfo:{} as user,
      host:'',
      routerArr:new Array<routerRef>()
    }
  },
  actions:{
    changeUser(data:user){
      this.userInfo = data
    },
    changeRouterArr(data:Array<routerRef>){
      this.routerArr = data
    }
  },
  persist: {
    enabled: true // true 表示开启持久化保存
  }
})