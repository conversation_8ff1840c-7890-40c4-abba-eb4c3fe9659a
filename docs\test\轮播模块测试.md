# 轮播模块功能测试

## 功能概述

新增了专门的轮播模块类型，与新闻模块和广告位置并列，提供独立的轮播文章配置。

## 新增功能

### 1. 轮播模块类型

**模块类型：** `banner`

**特点：**
- 专门用于文章轮播显示
- 优先显示置顶文章
- 如果置顶文章不足，自动补充普通文章
- 支持全宽/半宽配置
- 独立的配置界面

### 2. 前端配置界面

**可用模块区域：**
- 广告位置（绿色边框）
- 轮播模块（橙色边框）
- 新闻模块（蓝色边框）

**轮播模块配置项：**
- 模块名称：自定义轮播模块名称
- 启用状态：控制是否显示
- 显示宽度：全宽或半宽
- 轮播分类：选择文章来源分类
- 轮播数量：设置轮播文章数量

### 3. 后端处理逻辑

**轮播文章获取策略：**
1. 优先获取指定分类的置顶文章
2. 如果置顶文章数量不足，补充普通文章
3. 按照设置的数量限制返回结果

## 测试用例

### 1. 轮播模块添加测试

**测试步骤：**
1. 访问"网站配置 > 模块配置"
2. 在左侧点击"添加轮播模块"
3. 检查右侧布局预览

**预期结果：**
- 成功添加轮播模块
- 显示橙色边框和轮播图标
- 显示"轮播模块已添加"提示

### 2. 轮播模块配置测试

**测试步骤：**
1. 点击轮播模块的"配置"按钮
2. 修改模块名称为"首页轮播"
3. 选择文章分类
4. 设置轮播数量为5
5. 选择显示宽度
6. 保存配置

**预期结果：**
- 配置弹窗正确显示轮播模块配置项
- 显示轮播相关的说明信息
- 配置正确保存

### 3. 布局预览测试

**测试步骤：**
1. 添加多个不同类型的模块
2. 观察布局预览中的显示效果
3. 检查图标和边框颜色

**预期结果：**
- 轮播模块显示VideoPlay图标
- 橙色边框区分其他模块
- 显示轮播分类和数量信息

### 4. 宽度配置测试

**测试步骤：**
1. 配置轮播模块为半宽
2. 添加另一个半宽模块
3. 观察布局效果

**预期结果：**
- 两个半宽模块在同一行显示
- 宽度标签正确显示

### 5. 前台显示测试

**测试步骤：**
1. 在后台配置轮播模块
2. 确保有置顶文章和普通文章
3. 访问前台首页
4. 检查轮播显示效果

**预期结果：**
- 轮播模块正确显示
- 优先显示置顶文章
- 数量符合配置

## 数据结构

### 配置数据格式

```json
{
  "positions": {
    "banner_main": {
      "type": "banner",
      "enabled": true,
      "name": "主轮播",
      "width": "full",
      "catId": 1,
      "limit": 5
    }
  },
  "layout": ["banner_main"]
}
```

### 后端返回数据

```php
[
  'BannerArts' => [
    // 轮播文章数组
  ],
  'Articles' => [
    // 其他新闻模块数组
  ],
  'Entrances' => [
    // 广告数据
  ]
]
```

## 样式说明

### CSS类名

- `.banner-module`: 可用模块中的轮播模块样式
- `.banner-position`: 布局中的轮播模块样式
- 边框颜色：`#e6a23c`（橙色）

### 图标

- 可用模块：Picture图标
- 布局预览：VideoPlay图标

## 注意事项

### 1. 文章数据

- 确保数据库中有置顶文章（top=1）
- 确保选择的分类下有足够的文章
- 文章需要通过审核且在发布时间范围内

### 2. 配置兼容性

- 旧配置会自动升级支持轮播模块
- 默认配置包含轮播模块示例

### 3. 性能考虑

- 轮播模块会执行两次数据库查询（置顶+普通）
- 建议合理设置轮播数量

## 故障排除

### 1. 轮播不显示

**可能原因：**
- 轮播模块未启用
- 选择的分类下没有文章
- 文章未通过审核

**解决方法：**
- 检查模块启用状态
- 确认分类和文章数据
- 检查文章审核状态

### 2. 置顶文章不显示

**可能原因：**
- 没有设置置顶文章
- 置顶文章不在选择的分类中

**解决方法：**
- 在文章管理中设置置顶
- 确认置顶文章的分类

### 3. 配置保存失败

**可能原因：**
- 网络连接问题
- 后端接口错误

**解决方法：**
- 检查网络连接
- 查看浏览器控制台错误
- 检查后端日志
