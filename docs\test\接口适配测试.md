# 配置保存接口适配

## 问题分析

原有的配置保存接口期望的数据格式与我们发送的格式不匹配。

### 现有接口格式

**接口：** `comConfindSaveApi`

**期望参数：**
```javascript
{
  type: 'page_layout',
  data: JSON.stringify({
    page_layout: '配置JSON字符串'
  })
}
```

**后端处理逻辑：**
1. 接收 `type` 和 `data` 参数
2. 将 `data` 解析为对象
3. 遍历对象的每个键值对
4. 将每个键值对保存为单独的配置记录：
   - `type`: 'page_layout'
   - `ky`: 'page_layout' 
   - `val`: 配置JSON字符串

## 修改内容

### 1. 前端保存逻辑修改

**修改前：**
```javascript
const res: any = await comConfindSaveApi({
  code: 'page_layout',
  type: 'json',
  data: JSON.stringify(config)
})
```

**修改后：**
```javascript
const saveData = {
  page_layout: JSON.stringify(config)
}

const res: any = await comConfindSaveApi({
  type: 'page_layout',
  data: JSON.stringify(saveData)
})
```

### 2. 前端加载逻辑修改

**修改前：**
```javascript
const res: any = await comConfigFindApi({ code: 'page_layout' })
if (res.code === 0 && res.data) {
  const config = JSON.parse(res.data)
  // ...
}
```

**修改后：**
```javascript
const res: any = await comConfigFindApi({ type: 'page_layout' })
if (res.code === 0 && res.data) {
  const configData = res.data.page_layout
  if (configData) {
    const config = JSON.parse(configData)
    // ...
  }
}
```

### 3. 后端读取逻辑修改

**修改前：**
```php
$config = Db::table('aconfig')->where('code', 'page_layout')->value('data');
```

**修改后：**
```php
$configRecord = Db::table('aconfig')
  ->where('type', 'page_layout')
  ->where('ky', 'page_layout')
  ->value('val');
```

## 数据库存储格式

### 表结构：aconfig

| 字段 | 值 |
|------|-----|
| type | 'page_layout' |
| ky | 'page_layout' |
| val | '{"positions":{...},"layout":[...]}' |

### 配置数据格式

```json
{
  "positions": {
    "index_1": {
      "type": "entrance",
      "enabled": true,
      "name": "首页轮播广告",
      "width": "full"
    },
    "news_module_1": {
      "type": "news",
      "enabled": true,
      "name": "新闻模块",
      "width": "half",
      "catId": 1,
      "limit": 4
    }
  },
  "layout": ["index_1", "news_module_1"]
}
```

## 向后兼容性

### 1. 前端兼容性

在加载配置时，确保所有位置都有 `width` 字段：

```javascript
Object.keys(config.positions).forEach(key => {
  if (!config.positions[key].width) {
    config.positions[key].width = 'full'
  }
})
```

### 2. 后端兼容性

在读取配置时，为没有 `width` 字段的位置添加默认值：

```php
foreach ($configData['positions'] as &$position) {
  if (!isset($position['width'])) {
    $position['width'] = 'full';
  }
}
```

## 测试验证

### 1. 保存配置测试

**测试步骤：**
1. 配置多个模块，设置不同宽度
2. 点击"保存配置"
3. 检查数据库中的记录

**预期结果：**
- 在 `aconfig` 表中生成记录
- `type` = 'page_layout'
- `ky` = 'page_layout'
- `val` 包含完整的配置JSON

### 2. 加载配置测试

**测试步骤：**
1. 刷新页面
2. 检查配置是否正确加载
3. 验证所有字段都存在

**预期结果：**
- 配置正确从数据库加载
- 所有位置都有 `width` 字段
- 布局顺序保持正确

### 3. 兼容性测试

**测试步骤：**
1. 手动在数据库中插入没有 `width` 字段的旧配置
2. 刷新页面
3. 检查是否自动添加默认 `width` 值

**预期结果：**
- 旧配置能正常加载
- 自动添加 `width: 'full'`
- 不影响其他功能

## API接口说明

### comConfindSaveApi

**用途：** 保存配置数据

**参数：**
- `type`: 配置类型
- `data`: JSON字符串，包含要保存的键值对

### comConfigFindApi

**用途：** 获取配置数据

**参数：**
- `type`: 配置类型

**返回：**
- `data`: 对象，包含所有该类型的配置键值对

## 注意事项

1. **数据格式**：确保发送的数据格式符合接口期望
2. **JSON编码**：配置数据需要进行两次JSON编码
3. **错误处理**：添加适当的错误处理和用户提示
4. **性能考虑**：大配置数据的序列化和反序列化性能
