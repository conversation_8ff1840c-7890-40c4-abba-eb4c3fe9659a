<template>
  <el-dialog 
    v-model="dialogVisible" 
    :title="isEdit ? '编辑链接' : '添加链接'"
    width="600px"
    :before-close="handleClose"
  >
    <el-form 
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="链接名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入链接名称" />
      </el-form-item>

      <el-form-item label="链接地址" prop="link">
        <el-input v-model="formData.link" placeholder="请输入链接地址" />
      </el-form-item>

      <el-form-item label="链接位置" prop="pos">
        <el-input v-model="formData.pos" disabled />
      </el-form-item>
      
      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="formData.sort" :min="0" :max="999" />
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="链接图片">
        <div class="upload-area">
          <el-upload
            class="image-uploader"
            :action="uploadUrl"
            :show-file-list="false"
            :on-success="handleUploadSuccess"
            :before-upload="beforeUpload"
            accept="image/*"
          >
            <img v-if="formData.path" :src="store.hw + formData.path" class="uploaded-image" />
            <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tips">
            <p>点击上传图片</p>
            <p class="tip">支持 jpg、png 格式，建议尺寸 800x400px</p>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="描述">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入链接描述（可选）"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules, UploadProps } from 'element-plus'
import { bannersAddApi, bannersUpdateApi } from '@/api/api'
import { useStore } from '@/store/Index'

const store = useStore()
const emit = defineEmits(['refresh'])

// 弹窗控制
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  id: null,
  name: '',
  link: '',
  pos: '',
  sort: 0,
  status: 1,
  path: '',
  description: ''
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入链接名称', trigger: 'blur' }
  ],
  link: [
    { required: true, message: '请输入链接地址', trigger: 'blur' },
    { type: 'url', message: '请输入正确的链接格式', trigger: 'blur' }
  ],
  pos: [
    { required: true, message: '链接位置不能为空', trigger: 'blur' }
  ]
}

// 上传地址
const uploadUrl = computed(() => {
  return store.hw + '/api/upload' // 根据实际上传接口调整
})

// 打开弹窗
const open = (mode: 'add' | 'edit', data: any = null, positionKey: string = '') => {
  isEdit.value = mode === 'edit'
  
  if (mode === 'edit' && data) {
    // 编辑模式，填充数据
    Object.assign(formData, {
      id: data.id,
      name: data.name || '',
      link: data.link || '',
      pos: data.pos || positionKey,
      sort: data.sort || 0,
      status: data.status || 1,
      path: data.path || '',
      description: data.description || ''
    })
  } else {
    // 添加模式，重置表单
    resetForm()
    formData.pos = positionKey
  }
  
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: null,
    name: '',
    link: '',
    pos: '',
    sort: 0,
    status: 1,
    path: '',
    description: ''
  })
  formRef.value?.clearValidate()
}

// 上传成功回调
const handleUploadSuccess: UploadProps['onSuccess'] = (response) => {
  if (response.code === 0) {
    formData.path = response.data.path
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error(response.msg || '图片上传失败')
  }
}

// 上传前检查
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    submitting.value = true
    
    const submitData = { ...formData }
    delete submitData.id // 添加时不需要id
    
    let res: any
    if (isEdit.value) {
      res = await bannersUpdateApi({ id: formData.id, ...submitData })
    } else {
      res = await bannersAddApi(submitData)
    }
    
    if (res.code === 0) {
      ElMessage.success(isEdit.value ? '更新成功' : '添加成功')
      emit('refresh')
      handleClose()
    } else {
      ElMessage.error(res.msg || '操作失败')
    }
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    submitting.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 暴露方法
defineExpose({
  open
})
</script>

<style scoped>
.upload-area {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
}

.image-uploader:hover {
  border-color: #409eff;
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 80px;
  text-align: center;
  line-height: 80px;
  display: block;
}

.uploaded-image {
  width: 120px;
  height: 80px;
  object-fit: cover;
  display: block;
}

.upload-tips {
  flex: 1;
}

.upload-tips p {
  margin: 0 0 4px 0;
  color: #606266;
}

.upload-tips .tip {
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  text-align: right;
}
</style>
