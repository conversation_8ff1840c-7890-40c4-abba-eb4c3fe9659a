<template>
  <div>
    <div class="searchBox">
      <el-form ref="searchRef" :model="form" class="form">
        <el-form-item prop="name">
          <el-input prefix-icon="Search" v-model="form.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item prop="businessType">
          <el-select v-model="form.businessType" placeholder="请选择业务类型">
            <template #prefix>
              <el-icon ><Search /></el-icon>
            </template>
            <el-option label="其他" value="0" />
            <el-option label="新增" value="1" />
            <el-option label="修改" value="2" />
            <el-option label="删除" value="3" />
            <el-option label="授权" value="4" />
            <el-option label="导出" value="5" />
            <el-option label="导入" value="6" />
            <el-option label="强退" value="7" />
            <el-option label="生成代码" value="8" />
            <el-option label="清空数据" value="9" />
          </el-select>
        </el-form-item>
        <el-form-item prop="requestMethod">
          <el-select v-model="form.requestMethod" placeholder="请选择请求方式">
            <template #prefix>
              <el-icon ><Search /></el-icon>
            </template>
            <el-option label="GET" value="get" />
            <el-option label="POST" value="post" />
          </el-select>
        </el-form-item>
        <el-form-item prop="operatorType">
          <el-select v-model="form.operatorType" placeholder="请选择操作类别">
            <template #prefix>
              <el-icon ><Search /></el-icon>
            </template>
            <el-option label="其他" value="0" />
            <el-option label="后台用户" value="1" />
            <el-option label="手机端用户" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item prop="status">
          <el-select v-model="form.status" placeholder="请选择">
            <template #prefix>
              <el-icon ><Search /></el-icon>
            </template>
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getList">搜索</el-button>
          <span class="clear" @click="resetForm(searchRef)">清除选项</span>
        </el-form-item>
      </el-form>
    </div>
    <div class="toolBox">
      <el-button type="primary" icon="Plus" @click="">添加</el-button>
    </div>
    <div>
      <el-table 
        class="table"
        :data="tableData" 
        border 
        style="width: 100%"
        :header-cell-style="{ background: '#F2F2F2', textAlign: 'center' ,color:'#666'}"
        :cell-style="{ textAlign: 'center' }"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="account" label="账号" />
        <el-table-column prop="nicker" label="昵称" show-overflow-tooltip />
        <el-table-column  label="头像" >
          <template #default="scope">
            <el-avatar v-if='scope.row.avatar' :size="50" :src="store.hw+scope.row.avatar" />
          </template>
        </el-table-column>
        <el-table-column  label="超级管理" >
          <template #default="scope">
            {{scope.row.isRoot?'是':'否'}}
          </template>
        </el-table-column>
        <el-table-column  label="状态" >
          <template #default="scope">
            {{scope.row.status?'启用':'禁用'}}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="400">
          <template #default="scope" >
            <div v-if="scope.row.id != 1">
              <el-button type="warning" size="small" plain icon="refresh" @click="handleReset(scope.row)">重置密码</el-button>
              <!-- <el-button  type="primary" size="small" plain icon="Edit" @click="handleEdit(scope.row)">编辑</el-button> -->
              <el-button  type="danger" size="small" plain icon="Delete" @click="handleDel(scope.row.id)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <PageCom ref="pageCom" :total="total" @changePage='getList' />
    </div>
  </div>
</template>
<script lang="ts" setup>
import PageCom from '@/components/PageCom.vue'
import { ref,reactive,onMounted } from 'vue'
import { sysListApi,roleRemoveApi,adminResetApi } from '@/api/api'
import {  type FormInstance } from 'element-plus'
import { useStore } from '@/store/Index'

const store = useStore()
let total = ref(0)
interface RuleForm {
  title: string
  requestMethod: string
  businessType: string 
  operatorType: string 
  status: string 
}
const searchRef = ref<FormInstance>()
const form = reactive<RuleForm>({
  title:'',
  businessType:'',
  requestMethod:'',
  operatorType:'',
  status:'',
})

onMounted(()=>{
  getList()
})

const pageCom = ref()
let tableData = ref([])
const getList = ()=>{
  let data = {
    page:pageCom.value.currentPage,
    rows:pageCom.value.pageSize,
    ...form
  }
  sysListApi(data).then((res:any) =>{
    console.log('日志管理',res)
    if(res.code == 0){
      tableData.value = res.data.records
      total.value = res.data.total
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  getList()
}



interface msg{
  id:number,
  status:boolean
}


const handleDel = (id:number)=>{
  ElMessageBox.confirm('是否删除该数据？','提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'error',
    }
  ).then(() => {
    roleRemoveApi(id,{}).then((res:any)=>{
      if(res.code == 0){
        ElMessage.success('成功')
        getList()
      }else{
        ElMessage.error(res.msg)
      }
    })
    })
  .catch(() => {
    ElMessage.info('已取消')
  })
}

const handleReset = (row:msg)=>{
  ElMessageBox.confirm('是否重置密码？','提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    adminResetApi(row.id,{}).then((res:any)=>{
      if(res.code == 0){
        ElMessage.success('成功,新密码为：Admin123456!')
      }else{
        ElMessage.error(res.msg)
      }
    })
    })
  .catch(() => {
    ElMessage.info('已取消')
  })
}
</script>