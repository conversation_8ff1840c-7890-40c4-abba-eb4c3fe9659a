<template>
  <div>
    <div class="searchBox">
      <el-form ref="searchRef" :model="form" class="form">
        <el-form-item prop="name">
          <el-input prefix-icon="Search" v-model="form.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item prop="target">
          <el-select v-model="form.target" placeholder="请选择打开方式" clearable>
            <template #prefix>
              <el-icon ><Search /></el-icon>
            </template>
            <el-option label="新页面" :value="1" />
            <el-option label="当前页面" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item prop="status">
          <el-select v-model="form.status" placeholder="请选择状态" clearable>
            <template #prefix>
              <el-icon ><Search /></el-icon>
            </template>
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getList">搜索</el-button>
          <span class="clear" @click="resetForm(searchRef)">清除选项</span>
        </el-form-item>
      </el-form>
    </div>
    <div class="toolBox">
      <el-button type="primary" icon="Plus" @click="addLink">添加友链</el-button>
    </div>
    <div>
      <el-table 
        class="table"
        :data="tableData" 
        border 
        style="width: 100%"
        :header-cell-style="{ background: '#F2F2F2', textAlign: 'center' ,color:'#666'}"
        :cell-style="{ textAlign: 'center' }"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="sort" label="排序" width="60" />
        <el-table-column prop="name" label="名称" />
        <el-table-column prop="link" label="链接" show-overflow-tooltip />
        <el-table-column  label="打开方式" >
          <template #default="scope">
            {{scope.row.target == 1?'新页面':scope.row.target == 0 ?'当前页面':''}}
          </template>
        </el-table-column>
        <el-table-column  label="图片" >
          <template #default="scope">
            <el-avatar v-if='scope.row.path' :size="50" :src="store.hw+scope.row.path" />
          </template>
        </el-table-column>
        <el-table-column  label="状态" >
          <template #default="scope">
            {{scope.row.status == 1?'启用':scope.row.status == 0 ?'禁用':''}}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="200">
          <template #default="scope" >
            <el-button  type="primary" size="small" plain icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button  type="danger" size="small" plain icon="Delete" @click="handleDel(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <LinkPop ref="linkPop" @changeData="getList" />
  </div>
</template>
<script lang="ts" setup>
import LinkPop from './LinkPop.vue'
import { ref,reactive,onMounted } from 'vue'
import { linkApi,linkRemoveApi } from '@/api/api'
import {  type FormInstance } from 'element-plus'
import { useStore } from '@/store/Index'

const store = useStore()
interface RuleForm {
  name: string
  target ?: number
  status ?: number
}
const searchRef = ref<FormInstance>()
const form = reactive<RuleForm>({
  name:'',
})

onMounted(()=>{
  getList()
})

let tableData = ref([])
const getList = ()=>{
  let data = {
    ...form
  }
  linkApi(data).then((res:any) =>{
    console.log('友链管理',res)
    if(res.code == 0){
      tableData.value = res.data
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  getList()
}



interface msg{
  id:number,
  status:boolean
}

const linkPop = ref()
const addLink = ()=>{
  linkPop.value.isEdit = false 
  linkPop.value.open() 
}
const handleEdit = (row:msg)=>{
  linkPop.value.isEdit = true 
  linkPop.value.open() 
  linkPop.value.form = JSON.parse(JSON.stringify(row)) 
}

const handleDel = (id:number)=>{
  ElMessageBox.confirm('是否删除该数据？','提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'error',
    }
  ).then(() => {
    linkRemoveApi(id,{}).then((res:any)=>{
      if(res.code == 0){
        ElMessage.success(res.msg)
        getList()
      }else{
        ElMessage.error(res.msg)
      }
    })
    })
  .catch(() => {
    ElMessage.info('已取消')
  })
}

</script>