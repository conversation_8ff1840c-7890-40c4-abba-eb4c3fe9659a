<?php

namespace app\index\controller;

use app\common\bean\Article;
use app\common\bean\ArticleCat;
use app\common\bean\ArticleModelField;
use app\common\libs\Page;
use app\common\libs\Tool;
use app\common\repo\ArticleCatRepo;
use app\common\repo\ArticleRepo;
use app\common\repo\EntranceRepo;
use app\Request;
use think\Exception;
use think\facade\View;
use think\facade\Db;

class Index extends Base
{
    public function index()
    {
        // 获取页面布局配置
        $pageLayoutConfig = $this->getPageLayoutConfig();

        // 根据配置渲染页面内容
        $pageContent = $this->renderPageContent($pageLayoutConfig);

        View::assign($pageContent);
        return View::fetch();
    }

    public function search(Request $request)
    {
        $keyword = $request->get('keyword');
        if (!$keyword) return redirect('/');

        $Page = new Page($request->get('page/d'), 10);
        // 文章
        $ArticleRepo = new ArticleRepo();
        $time = date('Y-m-d H:i:s');
        $ArticleRepo->queryByPubtime('', $time);
        $ArticleRepo->queryByEndtime($time, '');
        $ArticleRepo->queryByDisplay(1);
        $ArticleRepo->queryByAudit(Article::AUDIT_PASS);
        $ArticleRepo->orderByTop();
        $ArticleRepo->orderBySort();
        $ArticleRepo->orderByPubtime();
        $ArticleRepo->queryByLikeKeywords($keyword);
        $ArticlePage = $ArticleRepo->query($Page);

        // 绑定数据
        View::assign([
            'ArticlePage' => $ArticlePage,
            'pagerender'  => $ArticlePage->render("?keyword={$keyword}&page=%s"),
        ]);
        return View::fetch();
    }

    public function catgory($id, $page = 1)
    {
        $ParentCatArray = array_reverse(getParentCatArray($id));
        if (!$ParentCatArray) return redirect('/');
        $ArticleCat = $ParentCatArray[count($ParentCatArray) - 1];
        if ($ArticleCat->type == ArticleCat::CatType_Link) return redirect($ArticleCat->link);

        if ($ArticleCat->pid) {
            // 非顶级分类（父分类s+同级分类）
            $ParentCat = findByCatId($ArticleCat->pid, false);
            $AssignCat = $ParentCat;
        } else {
            // 顶级分类（下级分类）
            $AssignCat = $ArticleCat;
        }
        $ArticleCat->getArticleModelField();
        // 文章
        $ArticleRepo = new ArticleRepo();
        $time = date('Y-m-d H:i:s');
        $ArticleRepo->queryByPubtime('', $time);
        $ArticleRepo->queryByEndtime($time, '');
        $ArticleRepo->queryByDisplay(1);
        $ArticleRepo->queryByAudit(Article::AUDIT_PASS);
        $ArticleRepo->orderByTop();
        $ArticleRepo->orderBySort();
        $ArticleRepo->orderByPubtime();
        $ArticleRepo->queryByCatId($ArticleCat->id);
        $Page = new Page(1, -1);
        if ($ArticleCat->type == ArticleCat::CatType_Page) {
            $ArticlePage = $ArticleRepo->query($Page);
        } else {
            $Page->current = $page;
            $Page->rows    = $ArticleCat->rows ?: 10;
            $ArticlePage   = $ArticleRepo->query($Page);
            if ($page != 1 && !$ArticlePage->data) {
                return redirect('?page=1');
            }
        }
        // 绑定数据
        View::assign([
            'CurrCat'  => $ArticleCat,
            'Parent'   => $AssignCat,
            'Parents'  => $ParentCatArray,
            'page'     => $ArticlePage->render(),
            'rows'     => $ArticlePage->rows,
            'count'    => $ArticlePage->total,
            'Articles' => $ArticlePage->data,
            'TList'    => ArticleCat::CatType_List,
            'TPage'    => ArticleCat::CatType_Page,
        ]);
        return View::fetch();
    }

    public function article($catId, $id)
    {
        // 文章
        $ArticleRepo = new ArticleRepo();
        $Article = $ArticleRepo->findById($id);
        if ($Article->isEmpty() || !$Article->display) {
            return redirect('/');
        }
        // 分类
        $ParentCatArray = array_reverse(getParentCatArray($Article->catId));
        $Cat = $ParentCatArray[count($ParentCatArray) - 1];
        $Cat->getArticleModelFieldWithData($Article->id, true);
        $ArticleModelFieldWithData = modelDataToResult($Cat->ArticleModelFieldWithData);
        $FieldWithDatas = [
            'tag'  => [],
            'link' => [],
            'text' => [],
            'file' => [],
            'img'  => [],
            'imgs' => [],
        ];
        $types = [
            'tagTypes'  => [
                ArticleModelField::FiledTypeNumber,
                ArticleModelField::FiledTypeRadio,
                ArticleModelField::FiledTypeSwitch,
                ArticleModelField::FiledTypeCheckbox,
                ArticleModelField::FiledTypeDate,
                ArticleModelField::FiledTypeDateTime,
            ],
            'fileTypes'  => [
                ArticleModelField::FiledTypeFile,
            ],
            'linkTypes'  => [
                ArticleModelField::FiledTypeLink,
            ],
            'textTypes'  => [
                ArticleModelField::FiledTypeText,
                ArticleModelField::FiledTypeTextarea,
                ArticleModelField::FiledTypeRichtext,
            ],
            'imgTypes'  => [
                ArticleModelField::FiledTypePic,
            ],
            'imgsTypes'  => [
                ArticleModelField::FiledTypePics,
            ],
        ];
        foreach ($ArticleModelFieldWithData as $key => $Item) {
            $typeTypeKey = '';
            if (in_array($Item->type, $types['tagTypes'])) {
                if (in_array($Item->type, [
                    ArticleModelField::FiledTypeRadio,
                    ArticleModelField::FiledTypeSwitch,
                    ArticleModelField::FiledTypeCheckbox,
                ])) {
                    $Item->data && $Item->data = $Item->data['option'];
                }
                $typeTypeKey = 'tag';
            } elseif (in_array($Item->type, $types['linkTypes'])) {
                $typeTypeKey = 'link';
            } elseif (in_array($Item->type, $types['fileTypes'])) {
                $typeTypeKey = 'file';
            } elseif (in_array($Item->type, $types['textTypes'])) {
                $typeTypeKey = 'text';
            } elseif (in_array($Item->type, $types['imgTypes'])) {
                $typeTypeKey = 'img';
            } elseif (in_array($Item->type, $types['imgsTypes'])) {
                $Item->data && $Item->data = json_decode($Item->data, true);
                $typeTypeKey = 'imgs';
            }
            $FieldWithDatas[$typeTypeKey][] = $Item;
        }
        // 增加点击量
        $ArticleViewsIds = [];
        $ArticleViews = session('ArticleViews');
        if ($ArticleViews) {
            $ArticleViewsIds = json_decode($ArticleViews, true);
        }
        if (!in_array($id, $ArticleViewsIds)) {
            $ArticleViewsIds[] = $id;
            $Article->views++;
            $ArticleRepo->save($Article);
            session('ArticleViews', json_encode($ArticleViewsIds));
        }
        // 绑定数据
        View::assign([
            'Article'        => $Article,
            'ArticleCat'     => $Cat,
            'Parents'        => $ParentCatArray,
            'FieldWithDatas' => $FieldWithDatas,
        ]);
        return View::fetch();
    }

    /**
     * 获取页面布局配置
     */
    private function getPageLayoutConfig()
    {
        // 默认配置
        $defaultConfig = [
            'positions' => [
                'index_1' => [
                    'type' => 'entrance',
                    'enabled' => true,
                    'name' => '首页轮播广告',
                    'width' => 'full'
                ],
                'banner_main' => [
                    'type' => 'banner',
                    'enabled' => true,
                    'name' => '主轮播',
                    'width' => 'full',
                    'catId' => 1,
                    'limit' => 5
                ],
                'news_module_1' => [
                    'type' => 'news',
                    'enabled' => true,
                    'name' => '新闻模块',
                    'width' => 'full',
                    'catId' => 1,
                    'limit' => 4
                ]
            ],
            'layout' => ['index_1', 'banner_main', 'news_module_1']
        ];

        try {
            // 从数据库获取配置，适配现有存储格式
            $configRecord = Db::table('aconfig')->where('type', 'page_layout')->where('ky', 'page_layout')->value('val');
            if ($configRecord) {
                $configData = json_decode($configRecord, true);
                if ($configData && is_array($configData)) {
                    // 确保配置结构完整
                    if (!isset($configData['positions'])) {
                        $configData['positions'] = $defaultConfig['positions'];
                    }
                    if (!isset($configData['layout'])) {
                        $configData['layout'] = $defaultConfig['layout'];
                    }
                    // 确保所有位置都有width字段（向后兼容）
                    foreach ($configData['positions'] as &$position) {
                        if (!isset($position['width'])) {
                            $position['width'] = 'full';
                        }
                    }
                    return $configData;
                }
            }
        } catch (\Exception $e) {
            // 配置读取失败，使用默认配置
        }

        return $defaultConfig;
    }

    /**
     * 根据配置渲染页面内容
     */
    private function renderPageContent($pageLayoutConfig)
    {
        // 获取所有广告数据
        $EntranceRepo = new EntranceRepo();
        $EntranceRepo->queryByStatus(1);
        $Entrances = $EntranceRepo->query();
        $EntrancesByPos = [];
        foreach ($Entrances as $Entrance) {
            $EntrancesByPos[$Entrance->pos][] = $Entrance;
        }

        // 初始化文章仓库
        $ArticleRepo = new ArticleRepo();
        $time = date('Y-m-d H:i:s');
        $ArticleRepo->queryByPubtime('', $time);
        $ArticleRepo->queryByEndtime($time, '');
        $ArticleRepo->queryByDisplay(1);
        $ArticleRepo->queryByAudit(Article::AUDIT_PASS);
        $ArticleRepo->orderByTop();
        $ArticleRepo->orderBySort();
        $ArticleRepo->orderByPubtime();

        // 处理新闻模块
        $BannerArticle = ['Arts' => []];
        $Articles = [];

        foreach ($pageLayoutConfig['layout'] as $positionId) {
            $position = $pageLayoutConfig['positions'][$positionId] ?? null;
            if (!$position || !$position['enabled']) {
                continue;
            }

            if ($position['type'] === 'news') {
                $catId = $position['catId'] ?? 1;
                $limit = $position['limit'] ?? 4;

                $catTree = findByCatId($catId, false);
                if ($catTree) {
                    $articleData = $this->getArticlesByCategory($ArticleRepo, $catTree, $limit);
                    $Articles[] = $articleData;
                }
            } elseif ($position['type'] === 'banner') {
                $catId = $position['catId'] ?? 1;
                $limit = $position['limit'] ?? 5;

                $catTree = findByCatId($catId, false);
                if ($catTree) {
                    // 轮播模块：优先获取置顶文章
                    $ArticleRepo->queryByTop(1);
                    $bannerData = $this->getArticlesByCategory($ArticleRepo, $catTree, $limit);
                    $ArticleRepo->clearQueryWhereByKey('top');

                    // 如果置顶文章不够，补充普通文章
                    if (count($bannerData['Arts']) < $limit && $limit > 0) {
                        $remainingLimit = $limit - count($bannerData['Arts']);
                        $normalData = $this->getArticlesByCategory($ArticleRepo, $catTree, $remainingLimit);
                        $bannerData['Arts'] = array_merge($bannerData['Arts'], $normalData['Arts']);
                    }

                    $BannerArticle = $bannerData;
                }
            }
        }

        // 按行组织布局，考虑宽度配置
        $layoutRows = $this->organizeLayoutByRows($pageLayoutConfig);

        // 过滤启用的广告位置
        $filteredEntrances = [];
        foreach ($pageLayoutConfig['layout'] as $positionId) {
            $position = $pageLayoutConfig['positions'][$positionId] ?? null;
            if ($position && $position['enabled'] && $position['type'] === 'entrance') {
                if (isset($EntrancesByPos[$positionId])) {
                    $filteredEntrances[$positionId] = $EntrancesByPos[$positionId];
                }
            }
        }

        return [
            'Poss' => config('entrancepos'),
            'Entrances' => $filteredEntrances,
            'BannerArts' => $BannerArticle['Arts'],
            'Articles' => $Articles,
            'PageLayout' => $pageLayoutConfig,
            'LayoutRows' => $layoutRows
        ];
    }

    /**
     * 按行组织布局，考虑宽度配置
     */
    private function organizeLayoutByRows($pageLayoutConfig)
    {
        $rows = [];
        $currentRow = [];
        $currentRowWidth = 0;

        foreach ($pageLayoutConfig['layout'] as $positionId) {
            $position = $pageLayoutConfig['positions'][$positionId] ?? null;
            if (!$position || !$position['enabled']) {
                continue;
            }

            $width = $position['width'] ?? 'full';
            $widthValue = ($width === 'full') ? 2 : 1; // full=2, half=1

            // 如果当前行放不下，开始新行
            if ($currentRowWidth + $widthValue > 2) {
                if (!empty($currentRow)) {
                    $rows[] = $currentRow;
                }
                $currentRow = [];
                $currentRowWidth = 0;
            }

            $currentRow[] = [
                'positionId' => $positionId,
                'position' => $position,
                'width' => $width
            ];
            $currentRowWidth += $widthValue;

            // 如果当前行已满，开始新行
            if ($currentRowWidth >= 2) {
                $rows[] = $currentRow;
                $currentRow = [];
                $currentRowWidth = 0;
            }
        }

        // 添加最后一行
        if (!empty($currentRow)) {
            $rows[] = $currentRow;
        }

        return $rows;
    }

    /**
     * 根据分类获取文章
     */
    private function getArticlesByCategory($ArticleRepo, $ArticleCatTree, $limit = 6)
    {
        if (!$ArticleCatTree) throw new Exception('文章分类不存在');
        $ArticleCats = Tool::tree_to_beans([$ArticleCatTree]);
        $ArticleCatIds = Tool::bean_column($ArticleCats, 'id');
        $ArticleRepo->queryByCatIds($ArticleCatIds);
        $ArticlePage = $ArticleRepo->query(new Page(1, $limit));
        $ArticleRepo->clearQueryWhereByKey('cat_id'); // 清空单独查询条件
        return [
            'Cat' => $ArticleCatTree,
            'Arts' => $ArticlePage->data,
        ];
    }
}
